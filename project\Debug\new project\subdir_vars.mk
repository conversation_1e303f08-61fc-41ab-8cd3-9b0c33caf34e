################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
CMD_SRCS += \
../new\ project/28335_RAM_lnk.cmd \
../new\ project/DSP2833x_Headers_nonBIOS.cmd 

LIB_SRCS += \
../new\ project/IQmath.lib 

ASM_SRCS += \
../new\ project/DSP2833x_ADC_cal.asm \
../new\ project/DSP2833x_CodeStartBranch.asm \
../new\ project/DSP2833x_usDelay.asm 

C_SRCS += \
../new\ project/DSP2833x_CpuTimers.c \
../new\ project/DSP2833x_DefaultIsr.c \
../new\ project/DSP2833x_GlobalVariableDefs.c \
../new\ project/DSP2833x_Gpio.c \
../new\ project/DSP2833x_PieCtrl.c \
../new\ project/DSP2833x_PieVect.c \
../new\ project/DSP2833x_SysCtrl.c 

OBJS += \
./new\ project/DSP2833x_ADC_cal.obj \
./new\ project/DSP2833x_CodeStartBranch.obj \
./new\ project/DSP2833x_CpuTimers.obj \
./new\ project/DSP2833x_DefaultIsr.obj \
./new\ project/DSP2833x_GlobalVariableDefs.obj \
./new\ project/DSP2833x_Gpio.obj \
./new\ project/DSP2833x_PieCtrl.obj \
./new\ project/DSP2833x_PieVect.obj \
./new\ project/DSP2833x_SysCtrl.obj \
./new\ project/DSP2833x_usDelay.obj 

ASM_DEPS += \
./new\ project/DSP2833x_ADC_cal.pp \
./new\ project/DSP2833x_CodeStartBranch.pp \
./new\ project/DSP2833x_usDelay.pp 

C_DEPS += \
./new\ project/DSP2833x_CpuTimers.pp \
./new\ project/DSP2833x_DefaultIsr.pp \
./new\ project/DSP2833x_GlobalVariableDefs.pp \
./new\ project/DSP2833x_Gpio.pp \
./new\ project/DSP2833x_PieCtrl.pp \
./new\ project/DSP2833x_PieVect.pp \
./new\ project/DSP2833x_SysCtrl.pp 

C_DEPS__QUOTED += \
"new project\DSP2833x_CpuTimers.pp" \
"new project\DSP2833x_DefaultIsr.pp" \
"new project\DSP2833x_GlobalVariableDefs.pp" \
"new project\DSP2833x_Gpio.pp" \
"new project\DSP2833x_PieCtrl.pp" \
"new project\DSP2833x_PieVect.pp" \
"new project\DSP2833x_SysCtrl.pp" 

OBJS__QUOTED += \
"new project\DSP2833x_ADC_cal.obj" \
"new project\DSP2833x_CodeStartBranch.obj" \
"new project\DSP2833x_CpuTimers.obj" \
"new project\DSP2833x_DefaultIsr.obj" \
"new project\DSP2833x_GlobalVariableDefs.obj" \
"new project\DSP2833x_Gpio.obj" \
"new project\DSP2833x_PieCtrl.obj" \
"new project\DSP2833x_PieVect.obj" \
"new project\DSP2833x_SysCtrl.obj" \
"new project\DSP2833x_usDelay.obj" 

ASM_DEPS__QUOTED += \
"new project\DSP2833x_ADC_cal.pp" \
"new project\DSP2833x_CodeStartBranch.pp" \
"new project\DSP2833x_usDelay.pp" 

ASM_SRCS__QUOTED += \
"../new project/DSP2833x_ADC_cal.asm" \
"../new project/DSP2833x_CodeStartBranch.asm" \
"../new project/DSP2833x_usDelay.asm" 

C_SRCS__QUOTED += \
"../new project/DSP2833x_CpuTimers.c" \
"../new project/DSP2833x_DefaultIsr.c" \
"../new project/DSP2833x_GlobalVariableDefs.c" \
"../new project/DSP2833x_Gpio.c" \
"../new project/DSP2833x_PieCtrl.c" \
"../new project/DSP2833x_PieVect.c" \
"../new project/DSP2833x_SysCtrl.c" 


