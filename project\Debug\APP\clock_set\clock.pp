# FIXED

APP/clock_set/clock.obj: ../APP/clock_set/clock.c
APP/clock_set/clock.obj: ../APP/clock_set/clock.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Device.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Adc.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_DevEmu.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_CpuTimers.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_ECan.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_ECap.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_DMA.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_EPwm.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_EQep.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Gpio.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_I2c.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_McBSP.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_PieCtrl.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_PieVect.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Spi.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Sci.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_SysCtrl.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_XIntrupt.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Xintf.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_Examples.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_GlobalPrototypes.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_ePwm_defines.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_Dma_defines.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_I2C_defines.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/LED.h
APP/clock_set/clock.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_DefaultISR.h
APP/clock_set/clock.obj: C:/ti/include/string.h
APP/clock_set/clock.obj: C:/ti/include/linkage.h

../APP/clock_set/clock.c: 
../APP/clock_set/clock.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Device.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Adc.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_DevEmu.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_CpuTimers.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_ECan.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_ECap.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_DMA.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_EPwm.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_EQep.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Gpio.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_I2c.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_McBSP.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_PieCtrl.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_PieVect.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Spi.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Sci.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_SysCtrl.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_XIntrupt.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Xintf.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_Examples.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_GlobalPrototypes.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_ePwm_defines.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_Dma_defines.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_I2C_defines.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/LED.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_DefaultISR.h: 
C:/ti/include/string.h: 
C:/ti/include/linkage.h: 
