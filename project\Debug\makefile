################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

CG_TOOL_ROOT := C:/ti

ORDERED_OBJS += \
$(GEN_CMDS__FLAG) \
"./APP/clock_set/clock.obj" \
"./APP/key/key.obj" \
"./APP/lcd/lcd1602.obj" \
"./APP/led_code/leds.obj" \
"./APP/timer/timer.obj" \
"./User/main.obj" \
"./new project/DSP2833x_usDelay.obj" \
"./new project/DSP2833x_SysCtrl.obj" \
"./new project/DSP2833x_PieVect.obj" \
"./new project/DSP2833x_PieCtrl.obj" \
"./new project/DSP2833x_Gpio.obj" \
"./new project/DSP2833x_GlobalVariableDefs.obj" \
"./new project/DSP2833x_DefaultIsr.obj" \
"./new project/DSP2833x_CpuTimers.obj" \
"./new project/DSP2833x_CodeStartBranch.obj" \
"./new project/DSP2833x_ADC_cal.obj" \
"../new project/28335_RAM_lnk.cmd" \
"../new project/DSP2833x_Headers_nonBIOS.cmd" \
"../new project/IQmath.lib" \
-l"libc.a" \

-include ../makefile.init

RM := DEL /F
RMDIR := RMDIR /S/Q

# All of the sources participating in the build are defined here
-include sources.mk
-include new\ project/subdir_vars.mk
-include User/subdir_vars.mk
-include APP/timer/subdir_vars.mk
-include APP/led_code/subdir_vars.mk
-include APP/lcd/subdir_vars.mk
-include APP/key/subdir_vars.mk
-include APP/clock_set/subdir_vars.mk
-include new\ project/subdir_rules.mk
-include User/subdir_rules.mk
-include APP/timer/subdir_rules.mk
-include APP/led_code/subdir_rules.mk
-include APP/lcd/subdir_rules.mk
-include APP/key/subdir_rules.mk
-include APP/clock_set/subdir_rules.mk
-include objects.mk

ifneq ($(MAKECMDGOALS),clean)
ifneq ($(strip $(S_DEPS)),)
-include $(S_DEPS)
endif
ifneq ($(strip $(S_UPPER_DEPS)),)
-include $(S_UPPER_DEPS)
endif
ifneq ($(strip $(S62_DEPS)),)
-include $(S62_DEPS)
endif
ifneq ($(strip $(C64_DEPS)),)
-include $(C64_DEPS)
endif
ifneq ($(strip $(ASM_DEPS)),)
-include $(ASM_DEPS)
endif
ifneq ($(strip $(CC_DEPS)),)
-include $(CC_DEPS)
endif
ifneq ($(strip $(S55_DEPS)),)
-include $(S55_DEPS)
endif
ifneq ($(strip $(C67_DEPS)),)
-include $(C67_DEPS)
endif
ifneq ($(strip $(C??_DEPS)),)
-include $(C??_DEPS)
endif
ifneq ($(strip $(CLA_DEPS)),)
-include $(CLA_DEPS)
endif
ifneq ($(strip $(CPP_DEPS)),)
-include $(CPP_DEPS)
endif
ifneq ($(strip $(S??_DEPS)),)
-include $(S??_DEPS)
endif
ifneq ($(strip $(C_DEPS)),)
-include $(C_DEPS)
endif
ifneq ($(strip $(C62_DEPS)),)
-include $(C62_DEPS)
endif
ifneq ($(strip $(CXX_DEPS)),)
-include $(CXX_DEPS)
endif
ifneq ($(strip $(C++_DEPS)),)
-include $(C++_DEPS)
endif
ifneq ($(strip $(ASM_UPPER_DEPS)),)
-include $(ASM_UPPER_DEPS)
endif
ifneq ($(strip $(K_DEPS)),)
-include $(K_DEPS)
endif
ifneq ($(strip $(C43_DEPS)),)
-include $(C43_DEPS)
endif
ifneq ($(strip $(S67_DEPS)),)
-include $(S67_DEPS)
endif
ifneq ($(strip $(SA_DEPS)),)
-include $(SA_DEPS)
endif
ifneq ($(strip $(S43_DEPS)),)
-include $(S43_DEPS)
endif
ifneq ($(strip $(OPT_DEPS)),)
-include $(OPT_DEPS)
endif
ifneq ($(strip $(S64_DEPS)),)
-include $(S64_DEPS)
endif
ifneq ($(strip $(C_UPPER_DEPS)),)
-include $(C_UPPER_DEPS)
endif
ifneq ($(strip $(C55_DEPS)),)
-include $(C55_DEPS)
endif
endif

-include ../makefile.defs

# Add inputs and outputs from these tool invocations to the build variables 

# All Target
all: sys_clock.out

# Tool invocations
sys_clock.out: $(OBJS) $(CMD_SRCS) $(LIB_SRCS) $(GEN_CMDS)
	@echo 'Building target: $@'
	@echo 'Invoking: C2000 Linker'
	"C:/ti/bin/cl2000" -v28 -ml -mt --float_support=fpu32 -g --display_error_number --diag_wrap=off --diag_warning=225 -z -m"sys_clock.map" --stack_size=0x300 --warn_sections -i"C:/ti/lib" -i"C:/ti/include" --reread_libs --display_error_number --diag_wrap=off --xml_link_info="sys_clock_linkInfo.xml" --rom_model -o "sys_clock.out" $(ORDERED_OBJS)
	@echo 'Finished building target: $@'
	@echo ' '

# Other Targets
clean:
	-$(RM) $(C2000_EXECUTABLE_OUTPUTS__QUOTED) "sys_clock.out"
	-$(RM) "new project\DSP2833x_CpuTimers.pp" "new project\DSP2833x_DefaultIsr.pp" "new project\DSP2833x_GlobalVariableDefs.pp" "new project\DSP2833x_Gpio.pp" "new project\DSP2833x_PieCtrl.pp" "new project\DSP2833x_PieVect.pp" "new project\DSP2833x_SysCtrl.pp" "User\main.pp" "APP\timer\timer.pp" "APP\led_code\leds.pp" "APP\lcd\lcd1602.pp" "APP\key\key.pp" "APP\clock_set\clock.pp" 
	-$(RM) "new project\DSP2833x_ADC_cal.obj" "new project\DSP2833x_CodeStartBranch.obj" "new project\DSP2833x_CpuTimers.obj" "new project\DSP2833x_DefaultIsr.obj" "new project\DSP2833x_GlobalVariableDefs.obj" "new project\DSP2833x_Gpio.obj" "new project\DSP2833x_PieCtrl.obj" "new project\DSP2833x_PieVect.obj" "new project\DSP2833x_SysCtrl.obj" "new project\DSP2833x_usDelay.obj" "User\main.obj" "APP\timer\timer.obj" "APP\led_code\leds.obj" "APP\lcd\lcd1602.obj" "APP\key\key.obj" "APP\clock_set\clock.obj" 
	-$(RM) "new project\DSP2833x_ADC_cal.pp" "new project\DSP2833x_CodeStartBranch.pp" "new project\DSP2833x_usDelay.pp" 
	-@echo 'Finished clean'
	-@echo ' '

.PHONY: all clean dependents
.SECONDARY:

-include ../makefile.targets

