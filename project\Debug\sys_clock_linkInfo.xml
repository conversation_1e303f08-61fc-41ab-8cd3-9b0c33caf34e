<?xml version="1.0"?>
<link_info>
   <banner>TMS320C2000 Linker PC v15.9.0.STS</banner>
   <copyright>Copyright (c) 1996-2015 Texas Instruments Incorporated</copyright>
   <link_time>0x686cc213</link_time>
   <link_errors>0x0</link_errors>
   <output_file>sys_clock.out</output_file>
   <entry_point>
      <name>_c_int00</name>
      <address>0x9904</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-2">
         <path>.\APP\clock_set\</path>
         <kind>object</kind>
         <file>clock.obj</file>
         <name>clock.obj</name>
      </input_file>
      <input_file id="fl-3">
         <path>.\APP\key\</path>
         <kind>object</kind>
         <file>key.obj</file>
         <name>key.obj</name>
      </input_file>
      <input_file id="fl-4">
         <path>.\APP\lcd\</path>
         <kind>object</kind>
         <file>lcd1602.obj</file>
         <name>lcd1602.obj</name>
      </input_file>
      <input_file id="fl-5">
         <path>.\APP\led_code\</path>
         <kind>object</kind>
         <file>leds.obj</file>
         <name>leds.obj</name>
      </input_file>
      <input_file id="fl-6">
         <path>.\APP\timer\</path>
         <kind>object</kind>
         <file>timer.obj</file>
         <name>timer.obj</name>
      </input_file>
      <input_file id="fl-7">
         <path>.\User\</path>
         <kind>object</kind>
         <file>main.obj</file>
         <name>main.obj</name>
      </input_file>
      <input_file id="fl-8">
         <path>.\new project\</path>
         <kind>object</kind>
         <file>DSP2833x_usDelay.obj</file>
         <name>DSP2833x_usDelay.obj</name>
      </input_file>
      <input_file id="fl-9">
         <path>.\new project\</path>
         <kind>object</kind>
         <file>DSP2833x_SysCtrl.obj</file>
         <name>DSP2833x_SysCtrl.obj</name>
      </input_file>
      <input_file id="fl-a">
         <path>.\new project\</path>
         <kind>object</kind>
         <file>DSP2833x_PieVect.obj</file>
         <name>DSP2833x_PieVect.obj</name>
      </input_file>
      <input_file id="fl-b">
         <path>.\new project\</path>
         <kind>object</kind>
         <file>DSP2833x_PieCtrl.obj</file>
         <name>DSP2833x_PieCtrl.obj</name>
      </input_file>
      <input_file id="fl-c">
         <path>.\new project\</path>
         <kind>object</kind>
         <file>DSP2833x_Gpio.obj</file>
         <name>DSP2833x_Gpio.obj</name>
      </input_file>
      <input_file id="fl-d">
         <path>.\new project\</path>
         <kind>object</kind>
         <file>DSP2833x_GlobalVariableDefs.obj</file>
         <name>DSP2833x_GlobalVariableDefs.obj</name>
      </input_file>
      <input_file id="fl-e">
         <path>.\new project\</path>
         <kind>object</kind>
         <file>DSP2833x_DefaultIsr.obj</file>
         <name>DSP2833x_DefaultIsr.obj</name>
      </input_file>
      <input_file id="fl-f">
         <path>.\new project\</path>
         <kind>object</kind>
         <file>DSP2833x_CpuTimers.obj</file>
         <name>DSP2833x_CpuTimers.obj</name>
      </input_file>
      <input_file id="fl-10">
         <path>.\new project\</path>
         <kind>object</kind>
         <file>DSP2833x_CodeStartBranch.obj</file>
         <name>DSP2833x_CodeStartBranch.obj</name>
      </input_file>
      <input_file id="fl-11">
         <path>.\new project\</path>
         <kind>object</kind>
         <file>DSP2833x_ADC_cal.obj</file>
         <name>DSP2833x_ADC_cal.obj</name>
      </input_file>
      <input_file id="fl-1d">
         <path>C:\ti\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>boot.obj</name>
      </input_file>
      <input_file id="fl-1e">
         <path>C:\ti\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>cpy_tbl.obj</name>
      </input_file>
      <input_file id="fl-1f">
         <path>C:\ti\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>cpy_utils.obj</name>
      </input_file>
      <input_file id="fl-20">
         <path>C:\ti\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>exit.obj</name>
      </input_file>
      <input_file id="fl-21">
         <path>C:\ti\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>i_div.obj</name>
      </input_file>
      <input_file id="fl-22">
         <path>C:\ti\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>_lock.obj</name>
      </input_file>
      <input_file id="fl-23">
         <path>C:\ti\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>args_main.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-c9">
         <name>codestart</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-10"/>
      </object_component>
      <object_component id="oc-6f">
         <name>ramfuncs</name>
         <load_address>0x805a</load_address>
         <run_address>0x805a</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.text:retain</name>
         <load_address>0x9000</load_address>
         <run_address>0x9000</run_address>
         <size>0x323</size>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.text</name>
         <load_address>0x9323</load_address>
         <run_address>0x9323</run_address>
         <size>0x1e8</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-101">
         <name>.text</name>
         <load_address>0x950b</load_address>
         <run_address>0x950b</run_address>
         <size>0xf8</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.text</name>
         <load_address>0x9603</load_address>
         <run_address>0x9603</run_address>
         <size>0xe3</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-104">
         <name>.text</name>
         <load_address>0x96e6</load_address>
         <run_address>0x96e6</run_address>
         <size>0xd2</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-100">
         <name>.text</name>
         <load_address>0x97b8</load_address>
         <run_address>0x97b8</run_address>
         <size>0xd1</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-109">
         <name>.text</name>
         <load_address>0x9889</load_address>
         <run_address>0x9889</run_address>
         <size>0x7b</size>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.text</name>
         <load_address>0x9904</load_address>
         <run_address>0x9904</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.text</name>
         <load_address>0x9954</load_address>
         <run_address>0x9954</run_address>
         <size>0x46</size>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-105">
         <name>.text</name>
         <load_address>0x999a</load_address>
         <run_address>0x999a</run_address>
         <size>0x45</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-102">
         <name>.text</name>
         <load_address>0x99df</load_address>
         <run_address>0x99df</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-106">
         <name>.text</name>
         <load_address>0x9a07</load_address>
         <run_address>0x9a07</run_address>
         <size>0x25</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.text</name>
         <load_address>0x9a2c</load_address>
         <run_address>0x9a2c</run_address>
         <size>0x22</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-103">
         <name>.text</name>
         <load_address>0x9a4e</load_address>
         <run_address>0x9a4e</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-63">
         <name>.text:retain</name>
         <load_address>0x9a6e</load_address>
         <run_address>0x9a6e</run_address>
         <size>0x1b</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-de">
         <name>.text</name>
         <load_address>0x9a89</load_address>
         <run_address>0x9a89</run_address>
         <size>0x1a</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.text</name>
         <load_address>0x9aa3</load_address>
         <run_address>0x9aa3</run_address>
         <size>0x19</size>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.text</name>
         <load_address>0x9abc</load_address>
         <run_address>0x9abc</run_address>
         <size>0x19</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-107">
         <name>.text</name>
         <load_address>0x9ad5</load_address>
         <run_address>0x9ad5</run_address>
         <size>0x9</size>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.text</name>
         <load_address>0x9ade</load_address>
         <run_address>0x9ade</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-10"/>
      </object_component>
      <object_component id="oc-61">
         <name>.cinit</name>
         <load_address>0x8000</load_address>
         <run_address>0x8000</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-69">
         <name>.cinit</name>
         <load_address>0x8020</load_address>
         <run_address>0x8020</run_address>
         <size>0xc</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.cinit</name>
         <load_address>0x802c</load_address>
         <run_address>0x802c</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.cinit:__lock</name>
         <load_address>0x8036</load_address>
         <run_address>0x8036</run_address>
         <size>0x5</size>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-108">
         <name>.cinit:__unlock</name>
         <load_address>0x803b</load_address>
         <run_address>0x803b</run_address>
         <size>0x5</size>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.switch:_data_proc</name>
         <load_address>0x8042</load_address>
         <run_address>0x8042</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <run_address>0x400</run_address>
         <size>0x0</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <run_address>0x400</run_address>
         <size>0x0</size>
      </object_component>
      <object_component id="oc-62">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0xc02b</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0xc000</run_address>
         <size>0x2b</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0xc040</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0xc034</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.ebss:__unlock</name>
         <uninitialized>true</uninitialized>
         <run_address>0xc03a</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.ebss:__lock</name>
         <uninitialized>true</uninitialized>
         <run_address>0xc038</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.econst</name>
         <load_address>0xd000</load_address>
         <run_address>0xd000</run_address>
         <size>0x100</size>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.reset</name>
         <load_address>0x3fffc0</load_address>
         <run_address>0x3fffc0</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.adc_cal</name>
         <load_address>0x380080</load_address>
         <run_address>0x380080</run_address>
         <size>0x7</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-ba">
         <name>PieVectTableFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xd00</run_address>
         <size>0x100</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-b6">
         <name>DevEmuRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x880</run_address>
         <size>0xd0</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-8c">
         <name>FlashRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xa80</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-96">
         <name>CsmRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xae0</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-95">
         <name>AdcMirrorFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xb00</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-99">
         <name>XintfRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xb20</run_address>
         <size>0x1e</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-8d">
         <name>CpuTimer0RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xc00</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-8b">
         <name>CpuTimer1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xc08</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-8a">
         <name>CpuTimer2RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xc10</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-97">
         <name>PieCtrlRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xce0</run_address>
         <size>0x1a</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-b7">
         <name>DmaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x1000</run_address>
         <size>0xe0</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-a9">
         <name>McbspaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5000</run_address>
         <size>0x25</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-aa">
         <name>McbspbRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5040</run_address>
         <size>0x25</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-ad">
         <name>ECanaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6000</run_address>
         <size>0x34</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-b0">
         <name>ECanaLAMRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6040</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-b9">
         <name>ECanaMboxesFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6100</run_address>
         <size>0x100</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-b1">
         <name>ECanaMOTSRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6080</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-ae">
         <name>ECanaMOTORegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x60c0</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-ac">
         <name>ECanbRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6200</run_address>
         <size>0x34</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-b4">
         <name>ECanbLAMRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6240</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-b8">
         <name>ECanbMboxesFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6300</run_address>
         <size>0x100</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-af">
         <name>ECanbMOTSRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6280</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-b2">
         <name>ECanbMOTORegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x62c0</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-a4">
         <name>EPwm1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6800</run_address>
         <size>0x22</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-a3">
         <name>EPwm2RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6840</run_address>
         <size>0x22</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-a5">
         <name>EPwm3RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6880</run_address>
         <size>0x22</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-a7">
         <name>EPwm4RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x68c0</run_address>
         <size>0x22</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-a6">
         <name>EPwm5RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6900</run_address>
         <size>0x22</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-a8">
         <name>EPwm6RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6940</run_address>
         <size>0x22</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-a0">
         <name>ECap1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6a00</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-9c">
         <name>ECap2RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6a20</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-9d">
         <name>ECap3RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6a40</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-9a">
         <name>ECap4RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6a60</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-9b">
         <name>ECap5RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6a80</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-a1">
         <name>ECap6RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6aa0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-b5">
         <name>EQep1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6b00</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-b3">
         <name>EQep2RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6b40</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-ab">
         <name>GpioCtrlRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6f80</run_address>
         <size>0x2e</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-9e">
         <name>GpioDataRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6fc0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-8f">
         <name>GpioIntRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6fe0</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-9f">
         <name>SysCtrlRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7010</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-92">
         <name>SpiaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7040</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-93">
         <name>SciaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7050</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-94">
         <name>XIntruptRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7070</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-98">
         <name>AdcRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7100</run_address>
         <size>0x1e</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-91">
         <name>ScibRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7750</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-90">
         <name>ScicRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7770</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-a2">
         <name>I2caRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7900</run_address>
         <size>0x22</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-8e">
         <name>CsmPwlFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x33fff8</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x631</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_info</name>
         <load_address>0x631</load_address>
         <run_address>0x631</run_address>
         <size>0x1dde</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-56">
         <name>.debug_info</name>
         <load_address>0x240f</load_address>
         <run_address>0x240f</run_address>
         <size>0x216f</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.debug_info</name>
         <load_address>0x457e</load_address>
         <run_address>0x457e</run_address>
         <size>0x1e79</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_info</name>
         <load_address>0x63f7</load_address>
         <run_address>0x63f7</run_address>
         <size>0x2ac3</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_info</name>
         <load_address>0x8eba</load_address>
         <run_address>0x8eba</run_address>
         <size>0xa3b</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_info</name>
         <load_address>0x98f5</load_address>
         <run_address>0x98f5</run_address>
         <size>0xfa</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-74">
         <name>.debug_info</name>
         <load_address>0x99ef</load_address>
         <run_address>0x99ef</run_address>
         <size>0x1fee</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-79">
         <name>.debug_info</name>
         <load_address>0xb9dd</load_address>
         <run_address>0xb9dd</run_address>
         <size>0x22d5</size>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_info</name>
         <load_address>0xdcb2</load_address>
         <run_address>0xdcb2</run_address>
         <size>0xb7b</size>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_info</name>
         <load_address>0xe82d</load_address>
         <run_address>0xe82d</run_address>
         <size>0x1ba1</size>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_info</name>
         <load_address>0x103ce</load_address>
         <run_address>0x103ce</run_address>
         <size>0x13222</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_info</name>
         <load_address>0x235f0</load_address>
         <run_address>0x235f0</run_address>
         <size>0x27f2</size>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_info</name>
         <load_address>0x25de2</load_address>
         <run_address>0x25de2</run_address>
         <size>0x9d3</size>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_info</name>
         <load_address>0x267b5</load_address>
         <run_address>0x267b5</run_address>
         <size>0x13d</size>
         <input_file_ref idref="fl-10"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_info</name>
         <load_address>0x268f2</load_address>
         <run_address>0x268f2</run_address>
         <size>0xfa</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_info</name>
         <load_address>0x269ec</load_address>
         <run_address>0x269ec</run_address>
         <size>0x133</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_info</name>
         <load_address>0x26b1f</load_address>
         <run_address>0x26b1f</run_address>
         <size>0x621</size>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_info</name>
         <load_address>0x27140</load_address>
         <run_address>0x27140</run_address>
         <size>0x10c</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.debug_info</name>
         <load_address>0x2724c</load_address>
         <run_address>0x2724c</run_address>
         <size>0x4d1</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.debug_info</name>
         <load_address>0x2771d</load_address>
         <run_address>0x2771d</run_address>
         <size>0x133</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.debug_info</name>
         <load_address>0x27850</load_address>
         <run_address>0x27850</run_address>
         <size>0x4fb</size>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.debug_info</name>
         <load_address>0x27d4b</load_address>
         <run_address>0x27d4b</run_address>
         <size>0x4c8</size>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-160">
         <name>.debug_info</name>
         <load_address>0x28213</load_address>
         <run_address>0x28213</run_address>
         <size>0x9b</size>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xe0</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_frame</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0xbc</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_frame</name>
         <load_address>0x19c</load_address>
         <run_address>0x19c</run_address>
         <size>0x107</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.debug_frame</name>
         <load_address>0x2a3</load_address>
         <run_address>0x2a3</run_address>
         <size>0xbc</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_frame</name>
         <load_address>0x35f</load_address>
         <run_address>0x35f</run_address>
         <size>0xee</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_frame</name>
         <load_address>0x44d</load_address>
         <run_address>0x44d</run_address>
         <size>0xd4</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-75">
         <name>.debug_frame</name>
         <load_address>0x521</load_address>
         <run_address>0x521</run_address>
         <size>0x118</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_frame</name>
         <load_address>0x639</load_address>
         <run_address>0x639</run_address>
         <size>0xa7</size>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_frame</name>
         <load_address>0x6e0</load_address>
         <run_address>0x6e0</run_address>
         <size>0xb6</size>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_frame</name>
         <load_address>0x796</load_address>
         <run_address>0x796</run_address>
         <size>0x8c</size>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_frame</name>
         <load_address>0x822</load_address>
         <run_address>0x822</run_address>
         <size>0xc28</size>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_frame</name>
         <load_address>0x144a</load_address>
         <run_address>0x144a</run_address>
         <size>0xbd</size>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_frame</name>
         <load_address>0x1507</load_address>
         <run_address>0x1507</run_address>
         <size>0xba</size>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_frame</name>
         <load_address>0x15c1</load_address>
         <run_address>0x15c1</run_address>
         <size>0xbf</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.debug_frame</name>
         <load_address>0x1680</load_address>
         <run_address>0x1680</run_address>
         <size>0xcb</size>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.debug_frame</name>
         <load_address>0x174b</load_address>
         <run_address>0x174b</run_address>
         <size>0xa1</size>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x12a</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_line</name>
         <load_address>0x12a</load_address>
         <run_address>0x12a</run_address>
         <size>0x75</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_line</name>
         <load_address>0x19f</load_address>
         <run_address>0x19f</run_address>
         <size>0x119</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_line</name>
         <load_address>0x2b8</load_address>
         <run_address>0x2b8</run_address>
         <size>0x8b</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_line</name>
         <load_address>0x343</load_address>
         <run_address>0x343</run_address>
         <size>0x16f</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_line</name>
         <load_address>0x4b2</load_address>
         <run_address>0x4b2</run_address>
         <size>0x1bd</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_line</name>
         <load_address>0x66f</load_address>
         <run_address>0x66f</run_address>
         <size>0x54</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_line</name>
         <load_address>0x6c3</load_address>
         <run_address>0x6c3</run_address>
         <size>0x151</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_line</name>
         <load_address>0x814</load_address>
         <run_address>0x814</run_address>
         <size>0x74</size>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_line</name>
         <load_address>0x888</load_address>
         <run_address>0x888</run_address>
         <size>0x92</size>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_line</name>
         <load_address>0x91a</load_address>
         <run_address>0x91a</run_address>
         <size>0x3f</size>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_line</name>
         <load_address>0x959</load_address>
         <run_address>0x959</run_address>
         <size>0x76e</size>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_line</name>
         <load_address>0x10c7</load_address>
         <run_address>0x10c7</run_address>
         <size>0x94</size>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_line</name>
         <load_address>0x115b</load_address>
         <run_address>0x115b</run_address>
         <size>0x6d</size>
         <input_file_ref idref="fl-10"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_line</name>
         <load_address>0x11c8</load_address>
         <run_address>0x11c8</run_address>
         <size>0x55</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_line</name>
         <load_address>0x121d</load_address>
         <run_address>0x121d</run_address>
         <size>0x7a</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_line</name>
         <load_address>0x1297</load_address>
         <run_address>0x1297</run_address>
         <size>0x6a</size>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_line</name>
         <load_address>0x1301</load_address>
         <run_address>0x1301</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_line</name>
         <load_address>0x1351</load_address>
         <run_address>0x1351</run_address>
         <size>0x65</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_line</name>
         <load_address>0x13b6</load_address>
         <run_address>0x13b6</run_address>
         <size>0x69</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_line</name>
         <load_address>0x141f</load_address>
         <run_address>0x141f</run_address>
         <size>0x5e</size>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.debug_line</name>
         <load_address>0x147d</load_address>
         <run_address>0x147d</run_address>
         <size>0x55</size>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xbf</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_abbrev</name>
         <load_address>0xbf</load_address>
         <run_address>0xbf</run_address>
         <size>0x113</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_abbrev</name>
         <load_address>0x1d2</load_address>
         <run_address>0x1d2</run_address>
         <size>0x126</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_abbrev</name>
         <load_address>0x2f8</load_address>
         <run_address>0x2f8</run_address>
         <size>0xf8</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_abbrev</name>
         <load_address>0x3f0</load_address>
         <run_address>0x3f0</run_address>
         <size>0x172</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_abbrev</name>
         <load_address>0x562</load_address>
         <run_address>0x562</run_address>
         <size>0xe5</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-72">
         <name>.debug_abbrev</name>
         <load_address>0x647</load_address>
         <run_address>0x647</run_address>
         <size>0x2f</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-77">
         <name>.debug_abbrev</name>
         <load_address>0x676</load_address>
         <run_address>0x676</run_address>
         <size>0x17b</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_abbrev</name>
         <load_address>0x7f1</load_address>
         <run_address>0x7f1</run_address>
         <size>0x116</size>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_abbrev</name>
         <load_address>0x907</load_address>
         <run_address>0x907</run_address>
         <size>0xc8</size>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_abbrev</name>
         <load_address>0x9cf</load_address>
         <run_address>0x9cf</run_address>
         <size>0xdc</size>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_abbrev</name>
         <load_address>0xaab</load_address>
         <run_address>0xaab</run_address>
         <size>0xb7</size>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_abbrev</name>
         <load_address>0xb62</load_address>
         <run_address>0xb62</run_address>
         <size>0xde</size>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_abbrev</name>
         <load_address>0xc40</load_address>
         <run_address>0xc40</run_address>
         <size>0xfd</size>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_abbrev</name>
         <load_address>0xd3d</load_address>
         <run_address>0xd3d</run_address>
         <size>0x21</size>
         <input_file_ref idref="fl-10"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_abbrev</name>
         <load_address>0xd5e</load_address>
         <run_address>0xd5e</run_address>
         <size>0x2f</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_abbrev</name>
         <load_address>0xd8d</load_address>
         <run_address>0xd8d</run_address>
         <size>0x46</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.debug_abbrev</name>
         <load_address>0xdd3</load_address>
         <run_address>0xdd3</run_address>
         <size>0xfa</size>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_abbrev</name>
         <load_address>0xecd</load_address>
         <run_address>0xecd</run_address>
         <size>0x32</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.debug_abbrev</name>
         <load_address>0xeff</load_address>
         <run_address>0xeff</run_address>
         <size>0x117</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_abbrev</name>
         <load_address>0x1016</load_address>
         <run_address>0x1016</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_abbrev</name>
         <load_address>0x104e</load_address>
         <run_address>0x104e</run_address>
         <size>0xb2</size>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.debug_abbrev</name>
         <load_address>0x1100</load_address>
         <run_address>0x1100</run_address>
         <size>0xfe</size>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-161">
         <name>.debug_abbrev</name>
         <load_address>0x11fe</load_address>
         <run_address>0x11fe</run_address>
         <size>0xf</size>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_aranges</name>
         <load_address>0x30</load_address>
         <run_address>0x30</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5a">
         <name>.debug_aranges</name>
         <load_address>0x58</load_address>
         <run_address>0x58</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_aranges</name>
         <load_address>0x98</load_address>
         <run_address>0x98</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_aranges</name>
         <load_address>0xf0</load_address>
         <run_address>0xf0</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_aranges</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-78">
         <name>.debug_aranges</name>
         <load_address>0x140</load_address>
         <run_address>0x140</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_aranges</name>
         <load_address>0x188</load_address>
         <run_address>0x188</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_aranges</name>
         <load_address>0x1a8</load_address>
         <run_address>0x1a8</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_aranges</name>
         <load_address>0x1d0</load_address>
         <run_address>0x1d0</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_aranges</name>
         <load_address>0x1e8</load_address>
         <run_address>0x1e8</run_address>
         <size>0x298</size>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_aranges</name>
         <load_address>0x480</load_address>
         <run_address>0x480</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_aranges</name>
         <load_address>0x4a8</load_address>
         <run_address>0x4a8</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-10"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_aranges</name>
         <load_address>0x4d0</load_address>
         <run_address>0x4d0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_aranges</name>
         <load_address>0x4f0</load_address>
         <run_address>0x4f0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_aranges</name>
         <load_address>0x510</load_address>
         <run_address>0x510</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_aranges</name>
         <load_address>0x530</load_address>
         <run_address>0x530</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.debug_aranges</name>
         <load_address>0x550</load_address>
         <run_address>0x550</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_aranges</name>
         <load_address>0x578</load_address>
         <run_address>0x578</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_aranges</name>
         <load_address>0x5a0</load_address>
         <run_address>0x5a0</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.debug_aranges</name>
         <load_address>0x5d0</load_address>
         <run_address>0x5d0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-23"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>codestart</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-c9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>ramfuncs</name>
         <load_address>0x805a</load_address>
         <run_address>0x805a</run_address>
         <size>0x4</size>
         <contents>
            <object_component_ref idref="oc-6f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.text</name>
         <load_address>0x9000</load_address>
         <run_address>0x9000</run_address>
         <size>0xae6</size>
         <contents>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-c8"/>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x8000</load_address>
         <run_address>0x8000</run_address>
         <size>0x42</size>
         <contents>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-108"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.switch</name>
         <load_address>0x8042</load_address>
         <run_address>0x8042</run_address>
         <size>0x18</size>
         <contents>
            <object_component_ref idref="oc-10a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x400</run_address>
         <size>0x300</size>
         <contents>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-15f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.ebss</name>
         <run_address>0xc000</run_address>
         <size>0x58</size>
         <contents>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-fd"/>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.econst</name>
         <load_address>0xd000</load_address>
         <run_address>0xd000</run_address>
         <size>0x100</size>
         <contents>
            <object_component_ref idref="oc-7a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.esysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>IQmath</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>IQmathTables</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>IQmathTables2</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>FPUmathTables</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>DMARAML4</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>DMARAML5</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>DMARAML6</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>DMARAML7</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14" display="no" color="cyan">
         <name>ZONE7DATA</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-15" display="no" color="cyan">
         <name>.reset</name>
         <load_address>0x3fffc0</load_address>
         <run_address>0x3fffc0</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-d4"/>
         </contents>
      </logical_group>
      <logical_group id="lg-16" display="no" color="cyan">
         <name>csm_rsvd</name>
         <run_address>0x33ff80</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-17" display="no" color="cyan">
         <name>csmpasswds</name>
         <run_address>0x33fff8</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-18" display="no" color="cyan">
         <name>.adc_cal</name>
         <load_address>0x380080</load_address>
         <run_address>0x380080</run_address>
         <size>0x7</size>
         <contents>
            <object_component_ref idref="oc-ce"/>
         </contents>
      </logical_group>
      <logical_group id="lg-19" display="no" color="cyan">
         <name>PieVectTableFile</name>
         <run_address>0xd00</run_address>
         <size>0x100</size>
         <contents>
            <object_component_ref idref="oc-ba"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1a" display="no" color="cyan">
         <name>DevEmuRegsFile</name>
         <run_address>0x880</run_address>
         <size>0xd0</size>
         <contents>
            <object_component_ref idref="oc-b6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1b" display="no" color="cyan">
         <name>FlashRegsFile</name>
         <run_address>0xa80</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-8c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1c" display="no" color="cyan">
         <name>CsmRegsFile</name>
         <run_address>0xae0</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-96"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1d" display="no" color="cyan">
         <name>AdcMirrorFile</name>
         <run_address>0xb00</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-95"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1e" display="no" color="cyan">
         <name>XintfRegsFile</name>
         <run_address>0xb20</run_address>
         <size>0x1e</size>
         <contents>
            <object_component_ref idref="oc-99"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1f" display="no" color="cyan">
         <name>CpuTimer0RegsFile</name>
         <run_address>0xc00</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-8d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-20" display="no" color="cyan">
         <name>CpuTimer1RegsFile</name>
         <run_address>0xc08</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-8b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-21" display="no" color="cyan">
         <name>CpuTimer2RegsFile</name>
         <run_address>0xc10</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-8a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-22" display="no" color="cyan">
         <name>PieCtrlRegsFile</name>
         <run_address>0xce0</run_address>
         <size>0x1a</size>
         <contents>
            <object_component_ref idref="oc-97"/>
         </contents>
      </logical_group>
      <logical_group id="lg-23" display="no" color="cyan">
         <name>DmaRegsFile</name>
         <run_address>0x1000</run_address>
         <size>0xe0</size>
         <contents>
            <object_component_ref idref="oc-b7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-24" display="no" color="cyan">
         <name>McbspaRegsFile</name>
         <run_address>0x5000</run_address>
         <size>0x25</size>
         <contents>
            <object_component_ref idref="oc-a9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-25" display="no" color="cyan">
         <name>McbspbRegsFile</name>
         <run_address>0x5040</run_address>
         <size>0x25</size>
         <contents>
            <object_component_ref idref="oc-aa"/>
         </contents>
      </logical_group>
      <logical_group id="lg-26" display="no" color="cyan">
         <name>ECanaRegsFile</name>
         <run_address>0x6000</run_address>
         <size>0x34</size>
         <contents>
            <object_component_ref idref="oc-ad"/>
         </contents>
      </logical_group>
      <logical_group id="lg-27" display="no" color="cyan">
         <name>ECanaLAMRegsFile</name>
         <run_address>0x6040</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-b0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-28" display="no" color="cyan">
         <name>ECanaMboxesFile</name>
         <run_address>0x6100</run_address>
         <size>0x100</size>
         <contents>
            <object_component_ref idref="oc-b9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-29" display="no" color="cyan">
         <name>ECanaMOTSRegsFile</name>
         <run_address>0x6080</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-b1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-2a" display="no" color="cyan">
         <name>ECanaMOTORegsFile</name>
         <run_address>0x60c0</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-ae"/>
         </contents>
      </logical_group>
      <logical_group id="lg-2b" display="no" color="cyan">
         <name>ECanbRegsFile</name>
         <run_address>0x6200</run_address>
         <size>0x34</size>
         <contents>
            <object_component_ref idref="oc-ac"/>
         </contents>
      </logical_group>
      <logical_group id="lg-2c" display="no" color="cyan">
         <name>ECanbLAMRegsFile</name>
         <run_address>0x6240</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-b4"/>
         </contents>
      </logical_group>
      <logical_group id="lg-2d" display="no" color="cyan">
         <name>ECanbMboxesFile</name>
         <run_address>0x6300</run_address>
         <size>0x100</size>
         <contents>
            <object_component_ref idref="oc-b8"/>
         </contents>
      </logical_group>
      <logical_group id="lg-2e" display="no" color="cyan">
         <name>ECanbMOTSRegsFile</name>
         <run_address>0x6280</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-af"/>
         </contents>
      </logical_group>
      <logical_group id="lg-2f" display="no" color="cyan">
         <name>ECanbMOTORegsFile</name>
         <run_address>0x62c0</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-b2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-30" display="no" color="cyan">
         <name>EPwm1RegsFile</name>
         <run_address>0x6800</run_address>
         <size>0x22</size>
         <contents>
            <object_component_ref idref="oc-a4"/>
         </contents>
      </logical_group>
      <logical_group id="lg-31" display="no" color="cyan">
         <name>EPwm2RegsFile</name>
         <run_address>0x6840</run_address>
         <size>0x22</size>
         <contents>
            <object_component_ref idref="oc-a3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-32" display="no" color="cyan">
         <name>EPwm3RegsFile</name>
         <run_address>0x6880</run_address>
         <size>0x22</size>
         <contents>
            <object_component_ref idref="oc-a5"/>
         </contents>
      </logical_group>
      <logical_group id="lg-33" display="no" color="cyan">
         <name>EPwm4RegsFile</name>
         <run_address>0x68c0</run_address>
         <size>0x22</size>
         <contents>
            <object_component_ref idref="oc-a7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-34" display="no" color="cyan">
         <name>EPwm5RegsFile</name>
         <run_address>0x6900</run_address>
         <size>0x22</size>
         <contents>
            <object_component_ref idref="oc-a6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-35" display="no" color="cyan">
         <name>EPwm6RegsFile</name>
         <run_address>0x6940</run_address>
         <size>0x22</size>
         <contents>
            <object_component_ref idref="oc-a8"/>
         </contents>
      </logical_group>
      <logical_group id="lg-36" display="no" color="cyan">
         <name>ECap1RegsFile</name>
         <run_address>0x6a00</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-a0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-37" display="no" color="cyan">
         <name>ECap2RegsFile</name>
         <run_address>0x6a20</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-9c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-38" display="no" color="cyan">
         <name>ECap3RegsFile</name>
         <run_address>0x6a40</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-9d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-39" display="no" color="cyan">
         <name>ECap4RegsFile</name>
         <run_address>0x6a60</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-9a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3a" display="no" color="cyan">
         <name>ECap5RegsFile</name>
         <run_address>0x6a80</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-9b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3b" display="no" color="cyan">
         <name>ECap6RegsFile</name>
         <run_address>0x6aa0</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-a1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3c" display="no" color="cyan">
         <name>EQep1RegsFile</name>
         <run_address>0x6b00</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-b5"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3d" display="no" color="cyan">
         <name>EQep2RegsFile</name>
         <run_address>0x6b40</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-b3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3e" display="no" color="cyan">
         <name>GpioCtrlRegsFile</name>
         <run_address>0x6f80</run_address>
         <size>0x2e</size>
         <contents>
            <object_component_ref idref="oc-ab"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3f" display="no" color="cyan">
         <name>GpioDataRegsFile</name>
         <run_address>0x6fc0</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-9e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-40" display="no" color="cyan">
         <name>GpioIntRegsFile</name>
         <run_address>0x6fe0</run_address>
         <size>0xa</size>
         <contents>
            <object_component_ref idref="oc-8f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-41" display="no" color="cyan">
         <name>SysCtrlRegsFile</name>
         <run_address>0x7010</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-9f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-42" display="no" color="cyan">
         <name>SpiaRegsFile</name>
         <run_address>0x7040</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-92"/>
         </contents>
      </logical_group>
      <logical_group id="lg-43" display="no" color="cyan">
         <name>SciaRegsFile</name>
         <run_address>0x7050</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-93"/>
         </contents>
      </logical_group>
      <logical_group id="lg-44" display="no" color="cyan">
         <name>XIntruptRegsFile</name>
         <run_address>0x7070</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-94"/>
         </contents>
      </logical_group>
      <logical_group id="lg-45" display="no" color="cyan">
         <name>AdcRegsFile</name>
         <run_address>0x7100</run_address>
         <size>0x1e</size>
         <contents>
            <object_component_ref idref="oc-98"/>
         </contents>
      </logical_group>
      <logical_group id="lg-46" display="no" color="cyan">
         <name>ScibRegsFile</name>
         <run_address>0x7750</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-91"/>
         </contents>
      </logical_group>
      <logical_group id="lg-47" display="no" color="cyan">
         <name>ScicRegsFile</name>
         <run_address>0x7770</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-90"/>
         </contents>
      </logical_group>
      <logical_group id="lg-48" display="no" color="cyan">
         <name>I2caRegsFile</name>
         <run_address>0x7900</run_address>
         <size>0x22</size>
         <contents>
            <object_component_ref idref="oc-a2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-49" display="no" color="cyan">
         <name>CsmPwlFile</name>
         <run_address>0x33fff8</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-8e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-154" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x282ae</size>
         <contents>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-160"/>
         </contents>
      </logical_group>
      <logical_group id="lg-156" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x17ec</size>
         <contents>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-f5"/>
         </contents>
      </logical_group>
      <logical_group id="lg-158" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x14d2</size>
         <contents>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-f6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-15a" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x120d</size>
         <contents>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-161"/>
         </contents>
      </logical_group>
      <logical_group id="lg-15c" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x5f0</size>
         <contents>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-5a"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-f8"/>
         </contents>
      </logical_group>
   </logical_group_list>
   <placement_map>
      <memory_area display="no" color="green">
         <name>BEGIN</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x2</length>
         <used_space>0x2</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x2</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>BOOT_RSVD</name>
         <page_id>0x0</page_id>
         <origin>0x2</origin>
         <length>0x4e</length>
         <used_space>0x0</used_space>
         <unused_space>0x4e</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMM0</name>
         <page_id>0x0</page_id>
         <origin>0x50</origin>
         <length>0x3b0</length>
         <used_space>0x0</used_space>
         <unused_space>0x3b0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAML0</name>
         <page_id>0x0</page_id>
         <origin>0x8000</origin>
         <length>0x1000</length>
         <used_space>0x5e</used_space>
         <unused_space>0xfa2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x8000</start_address>
               <size>0x42</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x8042</start_address>
               <size>0x18</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x805a</start_address>
               <size>0x4</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <available_space>
               <start_address>0x805e</start_address>
               <size>0xfa2</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAML1</name>
         <page_id>0x0</page_id>
         <origin>0x9000</origin>
         <length>0x1000</length>
         <used_space>0xae6</used_space>
         <unused_space>0x51a</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x9000</start_address>
               <size>0xae6</size>
               <logical_group_ref idref="lg-4"/>
            </allocated_space>
            <available_space>
               <start_address>0x9ae6</start_address>
               <size>0x51a</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAML2</name>
         <page_id>0x0</page_id>
         <origin>0xa000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAML3</name>
         <page_id>0x0</page_id>
         <origin>0xb000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE6A</name>
         <page_id>0x0</page_id>
         <origin>0x100000</origin>
         <length>0xfc00</length>
         <used_space>0x0</used_space>
         <unused_space>0xfc00</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>CSM_RSVD</name>
         <page_id>0x0</page_id>
         <origin>0x33ff80</origin>
         <length>0x76</length>
         <used_space>0x0</used_space>
         <unused_space>0x76</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CSM_PWL</name>
         <page_id>0x0</page_id>
         <origin>0x33fff8</origin>
         <length>0x8</length>
         <used_space>0x0</used_space>
         <unused_space>0x8</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ADC_CAL</name>
         <page_id>0x0</page_id>
         <origin>0x380080</origin>
         <length>0x9</length>
         <used_space>0x7</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x380080</start_address>
               <size>0x7</size>
               <logical_group_ref idref="lg-18"/>
            </allocated_space>
            <available_space>
               <start_address>0x380087</start_address>
               <size>0x2</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>IQTABLES</name>
         <page_id>0x0</page_id>
         <origin>0x3fe000</origin>
         <length>0xb50</length>
         <used_space>0x0</used_space>
         <unused_space>0xb50</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>IQTABLES2</name>
         <page_id>0x0</page_id>
         <origin>0x3feb50</origin>
         <length>0x8c</length>
         <used_space>0x0</used_space>
         <unused_space>0x8c</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FPUTABLES</name>
         <page_id>0x0</page_id>
         <origin>0x3febdc</origin>
         <length>0x6a0</length>
         <used_space>0x0</used_space>
         <unused_space>0x6a0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BOOTROM</name>
         <page_id>0x0</page_id>
         <origin>0x3ff27c</origin>
         <length>0xd44</length>
         <used_space>0x0</used_space>
         <unused_space>0xd44</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>RESET</name>
         <page_id>0x0</page_id>
         <origin>0x3fffc0</origin>
         <length>0x2</length>
         <used_space>0x0</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMM1</name>
         <page_id>0x1</page_id>
         <origin>0x400</origin>
         <length>0x400</length>
         <used_space>0x300</used_space>
         <unused_space>0x100</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x400</start_address>
               <size>0x300</size>
               <logical_group_ref idref="lg-8"/>
            </allocated_space>
            <available_space>
               <start_address>0x700</start_address>
               <size>0x100</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>DEV_EMU</name>
         <page_id>0x1</page_id>
         <origin>0x880</origin>
         <length>0x180</length>
         <used_space>0xd0</used_space>
         <unused_space>0xb0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x880</start_address>
               <size>0xd0</size>
               <logical_group_ref idref="lg-1a"/>
            </allocated_space>
            <available_space>
               <start_address>0x950</start_address>
               <size>0xb0</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>FLASH_REGS</name>
         <page_id>0x1</page_id>
         <origin>0xa80</origin>
         <length>0x60</length>
         <used_space>0x8</used_space>
         <unused_space>0x58</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xa80</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-1b"/>
            </allocated_space>
            <available_space>
               <start_address>0xa88</start_address>
               <size>0x58</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CSM</name>
         <page_id>0x1</page_id>
         <origin>0xae0</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xae0</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-1c"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ADC_MIRROR</name>
         <page_id>0x1</page_id>
         <origin>0xb00</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xb00</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-1d"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>XINTF</name>
         <page_id>0x1</page_id>
         <origin>0xb20</origin>
         <length>0x20</length>
         <used_space>0x1e</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xb20</start_address>
               <size>0x1e</size>
               <logical_group_ref idref="lg-1e"/>
            </allocated_space>
            <available_space>
               <start_address>0xb3e</start_address>
               <size>0x2</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CPU_TIMER0</name>
         <page_id>0x1</page_id>
         <origin>0xc00</origin>
         <length>0x8</length>
         <used_space>0x8</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xc00</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-1f"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CPU_TIMER1</name>
         <page_id>0x1</page_id>
         <origin>0xc08</origin>
         <length>0x8</length>
         <used_space>0x8</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xc08</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-20"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CPU_TIMER2</name>
         <page_id>0x1</page_id>
         <origin>0xc10</origin>
         <length>0x8</length>
         <used_space>0x8</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xc10</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-21"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>PIE_CTRL</name>
         <page_id>0x1</page_id>
         <origin>0xce0</origin>
         <length>0x20</length>
         <used_space>0x1a</used_space>
         <unused_space>0x6</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xce0</start_address>
               <size>0x1a</size>
               <logical_group_ref idref="lg-22"/>
            </allocated_space>
            <available_space>
               <start_address>0xcfa</start_address>
               <size>0x6</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>PIE_VECT</name>
         <page_id>0x1</page_id>
         <origin>0xd00</origin>
         <length>0x100</length>
         <used_space>0x100</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xd00</start_address>
               <size>0x100</size>
               <logical_group_ref idref="lg-19"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>DMA</name>
         <page_id>0x1</page_id>
         <origin>0x1000</origin>
         <length>0x200</length>
         <used_space>0xe0</used_space>
         <unused_space>0x120</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x1000</start_address>
               <size>0xe0</size>
               <logical_group_ref idref="lg-23"/>
            </allocated_space>
            <available_space>
               <start_address>0x10e0</start_address>
               <size>0x120</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>MCBSPA</name>
         <page_id>0x1</page_id>
         <origin>0x5000</origin>
         <length>0x40</length>
         <used_space>0x25</used_space>
         <unused_space>0x1b</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5000</start_address>
               <size>0x25</size>
               <logical_group_ref idref="lg-24"/>
            </allocated_space>
            <available_space>
               <start_address>0x5025</start_address>
               <size>0x1b</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>MCBSPB</name>
         <page_id>0x1</page_id>
         <origin>0x5040</origin>
         <length>0x40</length>
         <used_space>0x25</used_space>
         <unused_space>0x1b</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5040</start_address>
               <size>0x25</size>
               <logical_group_ref idref="lg-25"/>
            </allocated_space>
            <available_space>
               <start_address>0x5065</start_address>
               <size>0x1b</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECANA</name>
         <page_id>0x1</page_id>
         <origin>0x6000</origin>
         <length>0x40</length>
         <used_space>0x34</used_space>
         <unused_space>0xc</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6000</start_address>
               <size>0x34</size>
               <logical_group_ref idref="lg-26"/>
            </allocated_space>
            <available_space>
               <start_address>0x6034</start_address>
               <size>0xc</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECANA_LAM</name>
         <page_id>0x1</page_id>
         <origin>0x6040</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6040</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-27"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECANA_MOTS</name>
         <page_id>0x1</page_id>
         <origin>0x6080</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6080</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-29"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECANA_MOTO</name>
         <page_id>0x1</page_id>
         <origin>0x60c0</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x60c0</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-2a"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ECANA_MBOX</name>
         <page_id>0x1</page_id>
         <origin>0x6100</origin>
         <length>0x100</length>
         <used_space>0x100</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6100</start_address>
               <size>0x100</size>
               <logical_group_ref idref="lg-28"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECANB</name>
         <page_id>0x1</page_id>
         <origin>0x6200</origin>
         <length>0x40</length>
         <used_space>0x34</used_space>
         <unused_space>0xc</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6200</start_address>
               <size>0x34</size>
               <logical_group_ref idref="lg-2b"/>
            </allocated_space>
            <available_space>
               <start_address>0x6234</start_address>
               <size>0xc</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECANB_LAM</name>
         <page_id>0x1</page_id>
         <origin>0x6240</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6240</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-2c"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECANB_MOTS</name>
         <page_id>0x1</page_id>
         <origin>0x6280</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6280</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-2e"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECANB_MOTO</name>
         <page_id>0x1</page_id>
         <origin>0x62c0</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x62c0</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-2f"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ECANB_MBOX</name>
         <page_id>0x1</page_id>
         <origin>0x6300</origin>
         <length>0x100</length>
         <used_space>0x100</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6300</start_address>
               <size>0x100</size>
               <logical_group_ref idref="lg-2d"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>EPWM1</name>
         <page_id>0x1</page_id>
         <origin>0x6800</origin>
         <length>0x22</length>
         <used_space>0x22</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6800</start_address>
               <size>0x22</size>
               <logical_group_ref idref="lg-30"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>EPWM2</name>
         <page_id>0x1</page_id>
         <origin>0x6840</origin>
         <length>0x22</length>
         <used_space>0x22</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6840</start_address>
               <size>0x22</size>
               <logical_group_ref idref="lg-31"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>EPWM3</name>
         <page_id>0x1</page_id>
         <origin>0x6880</origin>
         <length>0x22</length>
         <used_space>0x22</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6880</start_address>
               <size>0x22</size>
               <logical_group_ref idref="lg-32"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>EPWM4</name>
         <page_id>0x1</page_id>
         <origin>0x68c0</origin>
         <length>0x22</length>
         <used_space>0x22</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x68c0</start_address>
               <size>0x22</size>
               <logical_group_ref idref="lg-33"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>EPWM5</name>
         <page_id>0x1</page_id>
         <origin>0x6900</origin>
         <length>0x22</length>
         <used_space>0x22</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6900</start_address>
               <size>0x22</size>
               <logical_group_ref idref="lg-34"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>EPWM6</name>
         <page_id>0x1</page_id>
         <origin>0x6940</origin>
         <length>0x22</length>
         <used_space>0x22</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6940</start_address>
               <size>0x22</size>
               <logical_group_ref idref="lg-35"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECAP1</name>
         <page_id>0x1</page_id>
         <origin>0x6a00</origin>
         <length>0x20</length>
         <used_space>0x20</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6a00</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-36"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECAP2</name>
         <page_id>0x1</page_id>
         <origin>0x6a20</origin>
         <length>0x20</length>
         <used_space>0x20</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6a20</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-37"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECAP3</name>
         <page_id>0x1</page_id>
         <origin>0x6a40</origin>
         <length>0x20</length>
         <used_space>0x20</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6a40</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-38"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECAP4</name>
         <page_id>0x1</page_id>
         <origin>0x6a60</origin>
         <length>0x20</length>
         <used_space>0x20</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6a60</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-39"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECAP5</name>
         <page_id>0x1</page_id>
         <origin>0x6a80</origin>
         <length>0x20</length>
         <used_space>0x20</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6a80</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-3a"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECAP6</name>
         <page_id>0x1</page_id>
         <origin>0x6aa0</origin>
         <length>0x20</length>
         <used_space>0x20</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6aa0</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-3b"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>EQEP1</name>
         <page_id>0x1</page_id>
         <origin>0x6b00</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6b00</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-3c"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>EQEP2</name>
         <page_id>0x1</page_id>
         <origin>0x6b40</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6b40</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-3d"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>GPIOCTRL</name>
         <page_id>0x1</page_id>
         <origin>0x6f80</origin>
         <length>0x40</length>
         <used_space>0x2e</used_space>
         <unused_space>0x12</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6f80</start_address>
               <size>0x2e</size>
               <logical_group_ref idref="lg-3e"/>
            </allocated_space>
            <available_space>
               <start_address>0x6fae</start_address>
               <size>0x12</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>GPIODAT</name>
         <page_id>0x1</page_id>
         <origin>0x6fc0</origin>
         <length>0x20</length>
         <used_space>0x20</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6fc0</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-3f"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>GPIOINT</name>
         <page_id>0x1</page_id>
         <origin>0x6fe0</origin>
         <length>0x20</length>
         <used_space>0xa</used_space>
         <unused_space>0x16</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6fe0</start_address>
               <size>0xa</size>
               <logical_group_ref idref="lg-40"/>
            </allocated_space>
            <available_space>
               <start_address>0x6fea</start_address>
               <size>0x16</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>SYSTEM</name>
         <page_id>0x1</page_id>
         <origin>0x7010</origin>
         <length>0x20</length>
         <used_space>0x20</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7010</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-41"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>SPIA</name>
         <page_id>0x1</page_id>
         <origin>0x7040</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7040</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-42"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>SCIA</name>
         <page_id>0x1</page_id>
         <origin>0x7050</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7050</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-43"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>XINTRUPT</name>
         <page_id>0x1</page_id>
         <origin>0x7070</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7070</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-44"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ADC</name>
         <page_id>0x1</page_id>
         <origin>0x7100</origin>
         <length>0x20</length>
         <used_space>0x1e</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7100</start_address>
               <size>0x1e</size>
               <logical_group_ref idref="lg-45"/>
            </allocated_space>
            <available_space>
               <start_address>0x711e</start_address>
               <size>0x2</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>SCIB</name>
         <page_id>0x1</page_id>
         <origin>0x7750</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7750</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-46"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>SCIC</name>
         <page_id>0x1</page_id>
         <origin>0x7770</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7770</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-47"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>I2CA</name>
         <page_id>0x1</page_id>
         <origin>0x7900</origin>
         <length>0x40</length>
         <used_space>0x22</used_space>
         <unused_space>0x1e</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7900</start_address>
               <size>0x22</size>
               <logical_group_ref idref="lg-48"/>
            </allocated_space>
            <available_space>
               <start_address>0x7922</start_address>
               <size>0x1e</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAML4</name>
         <page_id>0x1</page_id>
         <origin>0xc000</origin>
         <length>0x1000</length>
         <used_space>0x58</used_space>
         <unused_space>0xfa8</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xc000</start_address>
               <size>0x58</size>
               <logical_group_ref idref="lg-9"/>
            </allocated_space>
            <available_space>
               <start_address>0xc058</start_address>
               <size>0xfa8</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAML5</name>
         <page_id>0x1</page_id>
         <origin>0xd000</origin>
         <length>0x1000</length>
         <used_space>0x100</used_space>
         <unused_space>0xf00</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xd000</start_address>
               <size>0x100</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <available_space>
               <start_address>0xd100</start_address>
               <size>0xf00</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAML6</name>
         <page_id>0x1</page_id>
         <origin>0xe000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAML7</name>
         <page_id>0x1</page_id>
         <origin>0xf000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE7B</name>
         <page_id>0x1</page_id>
         <origin>0x20fc00</origin>
         <length>0x400</length>
         <used_space>0x0</used_space>
         <unused_space>0x400</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CSM_PWL</name>
         <page_id>0x1</page_id>
         <origin>0x33fff8</origin>
         <length>0x8</length>
         <used_space>0x8</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x33fff8</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-49"/>
            </allocated_space>
         </usage_details>
      </memory_area>
   </placement_map>
   <symbol_table>
      <symbol id="sm-0">
         <name>cinit</name>
         <value>0x8000</value>
      </symbol>
      <symbol id="sm-1">
         <name>___cinit__</name>
         <value>0x8000</value>
      </symbol>
      <symbol id="sm-2">
         <name>pinit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3">
         <name>___pinit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-4">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-5">
         <name>___binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-6">
         <name>__STACK_SIZE</name>
         <value>0x300</value>
      </symbol>
      <symbol id="sm-7">
         <name>__STACK_END</name>
         <value>0x700</value>
      </symbol>
      <symbol id="sm-8">
         <name>___c_args__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-9">
         <name>.text</name>
         <value>0x9000</value>
      </symbol>
      <symbol id="sm-a">
         <name>___text__</name>
         <value>0x9000</value>
      </symbol>
      <symbol id="sm-b">
         <name>etext</name>
         <value>0x9ae6</value>
      </symbol>
      <symbol id="sm-c">
         <name>___etext__</name>
         <value>0x9ae6</value>
      </symbol>
      <symbol id="sm-c7">
         <name>_str_add2</name>
         <value>0x969d</value>
         <object_component_ref idref="oc-ff"/>
      </symbol>
      <symbol id="sm-c8">
         <name>_str_add1</name>
         <value>0x9654</value>
         <object_component_ref idref="oc-ff"/>
      </symbol>
      <symbol id="sm-c9">
         <name>_num_to_str</name>
         <value>0x9603</value>
         <object_component_ref idref="oc-ff"/>
      </symbol>
      <symbol id="sm-d9">
         <name>_key_read</name>
         <value>0x9a19</value>
         <object_component_ref idref="oc-106"/>
      </symbol>
      <symbol id="sm-da">
         <name>_key_init</name>
         <value>0x9a07</value>
         <object_component_ref idref="oc-106"/>
      </symbol>
      <symbol id="sm-ef">
         <name>_LCD_write_command</name>
         <value>0x97f1</value>
         <object_component_ref idref="oc-100"/>
      </symbol>
      <symbol id="sm-f0">
         <name>_Init_Port</name>
         <value>0x97b8</value>
         <object_component_ref idref="oc-100"/>
      </symbol>
      <symbol id="sm-f1">
         <name>_LCD_init</name>
         <value>0x9861</value>
         <object_component_ref idref="oc-100"/>
      </symbol>
      <symbol id="sm-f2">
         <name>_show</name>
         <value>0x97de</value>
         <object_component_ref idref="oc-100"/>
      </symbol>
      <symbol id="sm-f3">
         <name>_LCD_write_data</name>
         <value>0x9829</value>
         <object_component_ref idref="oc-100"/>
      </symbol>
      <symbol id="sm-108">
         <name>_led_init</name>
         <value>0x99c1</value>
         <object_component_ref idref="oc-105"/>
      </symbol>
      <symbol id="sm-109">
         <name>_led_ctrl</name>
         <value>0x999a</value>
         <object_component_ref idref="oc-105"/>
      </symbol>
      <symbol id="sm-134">
         <name>_led_flag</name>
         <value>0xc02c</value>
         <object_component_ref idref="oc-62"/>
      </symbol>
      <symbol id="sm-135">
         <name>_TIM0_IRQn</name>
         <value>0x9a6e</value>
         <object_component_ref idref="oc-63"/>
      </symbol>
      <symbol id="sm-136">
         <name>_day</name>
         <value>0xc032</value>
         <object_component_ref idref="oc-62"/>
      </symbol>
      <symbol id="sm-137">
         <name>_minute</name>
         <value>0xc02d</value>
         <object_component_ref idref="oc-62"/>
      </symbol>
      <symbol id="sm-138">
         <name>_second</name>
         <value>0xc02f</value>
         <object_component_ref idref="oc-62"/>
      </symbol>
      <symbol id="sm-139">
         <name>_TIM0_Init</name>
         <value>0x96e6</value>
         <object_component_ref idref="oc-104"/>
      </symbol>
      <symbol id="sm-13a">
         <name>_month</name>
         <value>0xc030</value>
         <object_component_ref idref="oc-62"/>
      </symbol>
      <symbol id="sm-13b">
         <name>_hour</name>
         <value>0xc02b</value>
         <object_component_ref idref="oc-62"/>
      </symbol>
      <symbol id="sm-13c">
         <name>_data_proc</name>
         <value>0x9720</value>
         <object_component_ref idref="oc-104"/>
      </symbol>
      <symbol id="sm-13d">
         <name>_year</name>
         <value>0xc02e</value>
         <object_component_ref idref="oc-62"/>
      </symbol>
      <symbol id="sm-168">
         <name>_main</name>
         <value>0x94e2</value>
         <object_component_ref idref="oc-fc"/>
      </symbol>
      <symbol id="sm-169">
         <name>_key_proc</name>
         <value>0x9323</value>
         <object_component_ref idref="oc-fc"/>
      </symbol>
      <symbol id="sm-16a">
         <name>_key_val</name>
         <value>0xc002</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-16b">
         <name>_time</name>
         <value>0xc011</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-16c">
         <name>_str2</name>
         <value>0xc017</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-16d">
         <name>_str1</name>
         <value>0xc020</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-16e">
         <name>_clock_mode</name>
         <value>0xc000</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-16f">
         <name>_time_index</name>
         <value>0xc001</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-170">
         <name>_clock_proc</name>
         <value>0x9448</value>
         <object_component_ref idref="oc-fc"/>
      </symbol>
      <symbol id="sm-171">
         <name>_f</name>
         <value>0xc00b</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-172">
         <name>_e</name>
         <value>0xc009</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-173">
         <name>_d</name>
         <value>0xc005</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-174">
         <name>_c</name>
         <value>0xc003</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-175">
         <name>_b</name>
         <value>0xc007</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-176">
         <name>_a</name>
         <value>0xc00d</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-182">
         <name>_DSP28x_usDelay</name>
         <value>0x805a</value>
         <object_component_ref idref="oc-6f"/>
      </symbol>
      <symbol id="sm-198">
         <name>_InitPll</name>
         <value>0x9526</value>
         <object_component_ref idref="oc-101"/>
      </symbol>
      <symbol id="sm-199">
         <name>_DisableDog</name>
         <value>0x951e</value>
         <object_component_ref idref="oc-101"/>
      </symbol>
      <symbol id="sm-19a">
         <name>_InitPeripheralClocks</name>
         <value>0x9577</value>
         <object_component_ref idref="oc-101"/>
      </symbol>
      <symbol id="sm-19b">
         <name>_CsmUnlock</name>
         <value>0x95d2</value>
         <object_component_ref idref="oc-101"/>
      </symbol>
      <symbol id="sm-19c">
         <name>_ServiceDog</name>
         <value>0x9514</value>
         <object_component_ref idref="oc-101"/>
      </symbol>
      <symbol id="sm-19d">
         <name>_InitSysCtrl</name>
         <value>0x950b</value>
         <object_component_ref idref="oc-101"/>
      </symbol>
      <symbol id="sm-1ae">
         <name>_PieVectTableInit</name>
         <value>0xd000</value>
         <object_component_ref idref="oc-7a"/>
      </symbol>
      <symbol id="sm-1af">
         <name>_InitPieVectTable</name>
         <value>0x9a4e</value>
         <object_component_ref idref="oc-103"/>
      </symbol>
      <symbol id="sm-1bc">
         <name>_InitPieCtrl</name>
         <value>0x99df</value>
         <object_component_ref idref="oc-102"/>
      </symbol>
      <symbol id="sm-1bd">
         <name>_EnableInterrupts</name>
         <value>0x99fe</value>
         <object_component_ref idref="oc-102"/>
      </symbol>
      <symbol id="sm-230">
         <name>_GpioCtrlRegs</name>
         <value>0x6f80</value>
         <object_component_ref idref="oc-ab"/>
      </symbol>
      <symbol id="sm-231">
         <name>_PieVectTable</name>
         <value>0xd00</value>
         <object_component_ref idref="oc-ba"/>
      </symbol>
      <symbol id="sm-232">
         <name>_ECap4Regs</name>
         <value>0x6a60</value>
         <object_component_ref idref="oc-9a"/>
      </symbol>
      <symbol id="sm-233">
         <name>_CsmRegs</name>
         <value>0xae0</value>
         <object_component_ref idref="oc-96"/>
      </symbol>
      <symbol id="sm-234">
         <name>_ECanaLAMRegs</name>
         <value>0x6040</value>
         <object_component_ref idref="oc-b0"/>
      </symbol>
      <symbol id="sm-235">
         <name>_ECanbMOTORegs</name>
         <value>0x62c0</value>
         <object_component_ref idref="oc-b2"/>
      </symbol>
      <symbol id="sm-236">
         <name>_EPwm4Regs</name>
         <value>0x68c0</value>
         <object_component_ref idref="oc-a7"/>
      </symbol>
      <symbol id="sm-237">
         <name>_ECap5Regs</name>
         <value>0x6a80</value>
         <object_component_ref idref="oc-9b"/>
      </symbol>
      <symbol id="sm-238">
         <name>_CpuTimer1Regs</name>
         <value>0xc08</value>
         <object_component_ref idref="oc-8b"/>
      </symbol>
      <symbol id="sm-239">
         <name>_SysCtrlRegs</name>
         <value>0x7010</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-23a">
         <name>_EPwm5Regs</name>
         <value>0x6900</value>
         <object_component_ref idref="oc-a6"/>
      </symbol>
      <symbol id="sm-23b">
         <name>_SpiaRegs</name>
         <value>0x7040</value>
         <object_component_ref idref="oc-92"/>
      </symbol>
      <symbol id="sm-23c">
         <name>_ECanaMOTSRegs</name>
         <value>0x6080</value>
         <object_component_ref idref="oc-b1"/>
      </symbol>
      <symbol id="sm-23d">
         <name>_ECap6Regs</name>
         <value>0x6aa0</value>
         <object_component_ref idref="oc-a1"/>
      </symbol>
      <symbol id="sm-23e">
         <name>_DmaRegs</name>
         <value>0x1000</value>
         <object_component_ref idref="oc-b7"/>
      </symbol>
      <symbol id="sm-23f">
         <name>_FlashRegs</name>
         <value>0xa80</value>
         <object_component_ref idref="oc-8c"/>
      </symbol>
      <symbol id="sm-240">
         <name>_CpuTimer0Regs</name>
         <value>0xc00</value>
         <object_component_ref idref="oc-8d"/>
      </symbol>
      <symbol id="sm-241">
         <name>_DevEmuRegs</name>
         <value>0x880</value>
         <object_component_ref idref="oc-b6"/>
      </symbol>
      <symbol id="sm-242">
         <name>_McbspbRegs</name>
         <value>0x5040</value>
         <object_component_ref idref="oc-aa"/>
      </symbol>
      <symbol id="sm-243">
         <name>_EPwm6Regs</name>
         <value>0x6940</value>
         <object_component_ref idref="oc-a8"/>
      </symbol>
      <symbol id="sm-244">
         <name>_SciaRegs</name>
         <value>0x7050</value>
         <object_component_ref idref="oc-93"/>
      </symbol>
      <symbol id="sm-245">
         <name>_GpioDataRegs</name>
         <value>0x6fc0</value>
         <object_component_ref idref="oc-9e"/>
      </symbol>
      <symbol id="sm-246">
         <name>_CsmPwl</name>
         <value>0x33fff8</value>
         <object_component_ref idref="oc-8e"/>
      </symbol>
      <symbol id="sm-247">
         <name>_AdcRegs</name>
         <value>0x7100</value>
         <object_component_ref idref="oc-98"/>
      </symbol>
      <symbol id="sm-248">
         <name>_XIntruptRegs</name>
         <value>0x7070</value>
         <object_component_ref idref="oc-94"/>
      </symbol>
      <symbol id="sm-249">
         <name>_CpuTimer2Regs</name>
         <value>0xc10</value>
         <object_component_ref idref="oc-8a"/>
      </symbol>
      <symbol id="sm-24a">
         <name>_PieCtrlRegs</name>
         <value>0xce0</value>
         <object_component_ref idref="oc-97"/>
      </symbol>
      <symbol id="sm-24b">
         <name>_ECanbMOTSRegs</name>
         <value>0x6280</value>
         <object_component_ref idref="oc-af"/>
      </symbol>
      <symbol id="sm-24c">
         <name>_ECanaRegs</name>
         <value>0x6000</value>
         <object_component_ref idref="oc-ad"/>
      </symbol>
      <symbol id="sm-24d">
         <name>_AdcMirror</name>
         <value>0xb00</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-24e">
         <name>_ECanbMboxes</name>
         <value>0x6300</value>
         <object_component_ref idref="oc-b8"/>
      </symbol>
      <symbol id="sm-24f">
         <name>_XintfRegs</name>
         <value>0xb20</value>
         <object_component_ref idref="oc-99"/>
      </symbol>
      <symbol id="sm-250">
         <name>_ScicRegs</name>
         <value>0x7770</value>
         <object_component_ref idref="oc-90"/>
      </symbol>
      <symbol id="sm-251">
         <name>_ECap1Regs</name>
         <value>0x6a00</value>
         <object_component_ref idref="oc-a0"/>
      </symbol>
      <symbol id="sm-252">
         <name>_EQep1Regs</name>
         <value>0x6b00</value>
         <object_component_ref idref="oc-b5"/>
      </symbol>
      <symbol id="sm-253">
         <name>_McbspaRegs</name>
         <value>0x5000</value>
         <object_component_ref idref="oc-a9"/>
      </symbol>
      <symbol id="sm-254">
         <name>_EPwm1Regs</name>
         <value>0x6800</value>
         <object_component_ref idref="oc-a4"/>
      </symbol>
      <symbol id="sm-255">
         <name>_ECanbRegs</name>
         <value>0x6200</value>
         <object_component_ref idref="oc-ac"/>
      </symbol>
      <symbol id="sm-256">
         <name>_ScibRegs</name>
         <value>0x7750</value>
         <object_component_ref idref="oc-91"/>
      </symbol>
      <symbol id="sm-257">
         <name>_ECap2Regs</name>
         <value>0x6a20</value>
         <object_component_ref idref="oc-9c"/>
      </symbol>
      <symbol id="sm-258">
         <name>_GpioIntRegs</name>
         <value>0x6fe0</value>
         <object_component_ref idref="oc-8f"/>
      </symbol>
      <symbol id="sm-259">
         <name>_EQep2Regs</name>
         <value>0x6b40</value>
         <object_component_ref idref="oc-b3"/>
      </symbol>
      <symbol id="sm-25a">
         <name>_EPwm2Regs</name>
         <value>0x6840</value>
         <object_component_ref idref="oc-a3"/>
      </symbol>
      <symbol id="sm-25b">
         <name>_ECanaMboxes</name>
         <value>0x6100</value>
         <object_component_ref idref="oc-b9"/>
      </symbol>
      <symbol id="sm-25c">
         <name>_ECap3Regs</name>
         <value>0x6a40</value>
         <object_component_ref idref="oc-9d"/>
      </symbol>
      <symbol id="sm-25d">
         <name>_ECanaMOTORegs</name>
         <value>0x60c0</value>
         <object_component_ref idref="oc-ae"/>
      </symbol>
      <symbol id="sm-25e">
         <name>_EPwm3Regs</name>
         <value>0x6880</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-25f">
         <name>_ECanbLAMRegs</name>
         <value>0x6240</value>
         <object_component_ref idref="oc-b4"/>
      </symbol>
      <symbol id="sm-260">
         <name>_I2caRegs</name>
         <value>0x7900</value>
         <object_component_ref idref="oc-a2"/>
      </symbol>
      <symbol id="sm-2bc">
         <name>_ILLEGAL_ISR</name>
         <value>0x903c</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2bd">
         <name>_EPWM6_INT_ISR</name>
         <value>0x9172</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2be">
         <name>_DATALOG_ISR</name>
         <value>0x9014</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2bf">
         <name>_SPITXINTA_ISR</name>
         <value>0x91d6</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2c0">
         <name>_SPIRXINTA_ISR</name>
         <value>0x91cc</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2c1">
         <name>_DINTCH3_ISR</name>
         <value>0x921c</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2c2">
         <name>_XINT4_ISR</name>
         <value>0x92c6</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2c3">
         <name>_SEQ1INT_ISR</name>
         <value>0x90be</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2c4">
         <name>_ECAP3_INT_ISR</name>
         <value>0x9190</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2c5">
         <name>_INT13_ISR</name>
         <value>0x9000</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2c6">
         <name>_MXINTA_ISR</name>
         <value>0x91fe</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2c7">
         <name>_EPWM4_INT_ISR</name>
         <value>0x915e</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2c8">
         <name>_USER5_ISR</name>
         <value>0x906e</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2c9">
         <name>_XINT7_ISR</name>
         <value>0x92e4</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2ca">
         <name>_EMPTY_ISR</name>
         <value>0x9302</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2cb">
         <name>_EPWM5_TZINT_ISR</name>
         <value>0x912c</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2cc">
         <name>_EPWM4_TZINT_ISR</name>
         <value>0x9122</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2cd">
         <name>_ECAN0INTA_ISR</name>
         <value>0x9294</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2ce">
         <name>_EPWM6_TZINT_ISR</name>
         <value>0x9136</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2cf">
         <name>_EMUINT_ISR</name>
         <value>0x9028</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2d0">
         <name>_ECAP1_INT_ISR</name>
         <value>0x917c</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2d1">
         <name>_EPWM1_TZINT_ISR</name>
         <value>0x9104</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2d2">
         <name>_EQEP2_INT_ISR</name>
         <value>0x91c2</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2d3">
         <name>_USER11_ISR</name>
         <value>0x90aa</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2d4">
         <name>_EPWM3_TZINT_ISR</name>
         <value>0x9118</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2d5">
         <name>_USER4_ISR</name>
         <value>0x9064</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2d6">
         <name>_EPWM2_TZINT_ISR</name>
         <value>0x910e</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2d7">
         <name>_XINT6_ISR</name>
         <value>0x92da</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2d8">
         <name>_EPWM2_INT_ISR</name>
         <value>0x914a</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2d9">
         <name>_ECAN0INTB_ISR</name>
         <value>0x92a8</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2da">
         <name>_TINT0_ISR</name>
         <value>0x90f0</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2db">
         <name>_WAKEINT_ISR</name>
         <value>0x90fa</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2dc">
         <name>_DINTCH4_ISR</name>
         <value>0x9226</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2dd">
         <name>_USER10_ISR</name>
         <value>0x90a0</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2de">
         <name>_USER7_ISR</name>
         <value>0x9082</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2df">
         <name>_XINT1_ISR</name>
         <value>0x90d2</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2e0">
         <name>_ECAP6_INT_ISR</name>
         <value>0x91ae</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2e1">
         <name>_INT14_ISR</name>
         <value>0x900a</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2e2">
         <name>_MXINTB_ISR</name>
         <value>0x91ea</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2e3">
         <name>_DINTCH5_ISR</name>
         <value>0x9230</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2e4">
         <name>_USER6_ISR</name>
         <value>0x9078</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2e5">
         <name>_ECAP4_INT_ISR</name>
         <value>0x919a</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2e6">
         <name>_MRINTA_ISR</name>
         <value>0x91f4</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2e7">
         <name>_DINTCH6_ISR</name>
         <value>0x923a</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2e8">
         <name>_USER12_ISR</name>
         <value>0x90b4</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2e9">
         <name>_ADCINT_ISR</name>
         <value>0x90e6</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2ea">
         <name>_USER1_ISR</name>
         <value>0x9046</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2eb">
         <name>_XINT3_ISR</name>
         <value>0x92bc</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2ec">
         <name>_EPWM5_INT_ISR</name>
         <value>0x9168</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2ed">
         <name>_NMI_ISR</name>
         <value>0x9032</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2ee">
         <name>_SCITXINTB_ISR</name>
         <value>0x928a</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2ef">
         <name>_SCIRXINTB_ISR</name>
         <value>0x9280</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2f0">
         <name>_ECAN1INTA_ISR</name>
         <value>0x929e</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2f1">
         <name>_ECAP2_INT_ISR</name>
         <value>0x9186</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2f2">
         <name>_PIE_RESERVED</name>
         <value>0x930f</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2f3">
         <name>_I2CINT1A_ISR</name>
         <value>0x9244</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2f4">
         <name>_XINT2_ISR</name>
         <value>0x90dc</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2f5">
         <name>_I2CINT2A_ISR</name>
         <value>0x924e</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2f6">
         <name>_SCITXINTC_ISR</name>
         <value>0x9262</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2f7">
         <name>_SCIRXINTC_ISR</name>
         <value>0x9258</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2f8">
         <name>_RTOSINT_ISR</name>
         <value>0x901e</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2f9">
         <name>_EPWM3_INT_ISR</name>
         <value>0x9154</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2fa">
         <name>_ECAN1INTB_ISR</name>
         <value>0x92b2</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2fb">
         <name>_USER9_ISR</name>
         <value>0x9096</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2fc">
         <name>_USER3_ISR</name>
         <value>0x905a</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2fd">
         <name>_EQEP1_INT_ISR</name>
         <value>0x91b8</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2fe">
         <name>_MRINTB_ISR</name>
         <value>0x91e0</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2ff">
         <name>_DINTCH1_ISR</name>
         <value>0x9208</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-300">
         <name>_USER8_ISR</name>
         <value>0x908c</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-301">
         <name>_EPWM1_INT_ISR</name>
         <value>0x9140</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-302">
         <name>_SEQ2INT_ISR</name>
         <value>0x90c8</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-303">
         <name>_USER2_ISR</name>
         <value>0x9050</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-304">
         <name>_LUF_ISR</name>
         <value>0x92f8</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-305">
         <name>_LVF_ISR</name>
         <value>0x92ee</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-306">
         <name>_SCITXINTA_ISR</name>
         <value>0x9276</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-307">
         <name>_SCIRXINTA_ISR</name>
         <value>0x926c</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-308">
         <name>_rsvd_ISR</name>
         <value>0x9319</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-309">
         <name>_ECAP5_INT_ISR</name>
         <value>0x91a4</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-30a">
         <name>_DINTCH2_ISR</name>
         <value>0x9212</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-30b">
         <name>_XINT5_ISR</name>
         <value>0x92d0</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-31a">
         <name>_CpuTimer2</name>
         <value>0xc048</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-31b">
         <name>_CpuTimer0</name>
         <value>0xc050</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-31c">
         <name>_CpuTimer1</name>
         <value>0xc040</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-31d">
         <name>_InitCpuTimers</name>
         <value>0x9889</value>
         <object_component_ref idref="oc-109"/>
      </symbol>
      <symbol id="sm-31e">
         <name>_ConfigCpuTimer</name>
         <value>0x98ca</value>
         <object_component_ref idref="oc-109"/>
      </symbol>
      <symbol id="sm-32e">
         <name>code_start</name>
         <value>0x0</value>
         <object_component_ref idref="oc-c9"/>
      </symbol>
      <symbol id="sm-33a">
         <name>_ADC_cal</name>
         <value>0x380080</value>
         <object_component_ref idref="oc-ce"/>
      </symbol>
      <symbol id="sm-356">
         <name>_c_int00</name>
         <value>0x9904</value>
         <object_component_ref idref="oc-4b"/>
      </symbol>
      <symbol id="sm-357">
         <name>__stack</name>
         <value>0x400</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-36d">
         <name>_copy_in</name>
         <value>0x9954</value>
         <object_component_ref idref="oc-f9"/>
      </symbol>
      <symbol id="sm-37c">
         <name>_ppcopy</name>
         <value>0x9a89</value>
         <object_component_ref idref="oc-de"/>
      </symbol>
      <symbol id="sm-37d">
         <name>_dpcopy</name>
         <value>0x9a89</value>
         <object_component_ref idref="oc-de"/>
      </symbol>
      <symbol id="sm-37e">
         <name>_pdcopy</name>
         <value>0x9a89</value>
         <object_component_ref idref="oc-de"/>
      </symbol>
      <symbol id="sm-37f">
         <name>_ddcopy</name>
         <value>0x9a89</value>
         <object_component_ref idref="oc-de"/>
      </symbol>
      <symbol id="sm-393">
         <name>C$$EXIT</name>
         <value>0x9abc</value>
         <object_component_ref idref="oc-fb"/>
      </symbol>
      <symbol id="sm-394">
         <name>_exit</name>
         <value>0x9abe</value>
         <object_component_ref idref="oc-fb"/>
      </symbol>
      <symbol id="sm-395">
         <name>___TI_cleanup_ptr</name>
         <value>0xc034</value>
         <object_component_ref idref="oc-e5"/>
      </symbol>
      <symbol id="sm-396">
         <name>_abort</name>
         <value>0x9abc</value>
         <object_component_ref idref="oc-fb"/>
      </symbol>
      <symbol id="sm-397">
         <name>___TI_dtors_ptr</name>
         <value>0xc036</value>
         <object_component_ref idref="oc-e5"/>
      </symbol>
      <symbol id="sm-3a5">
         <name>I$$MOD</name>
         <value>0x9a3d</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-3a6">
         <name>I$$DIV</name>
         <value>0x9a2c</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-3bb">
         <name>__unlock</name>
         <value>0xc03a</value>
         <object_component_ref idref="oc-10b"/>
      </symbol>
      <symbol id="sm-3bc">
         <name>__lock</name>
         <value>0xc038</value>
         <object_component_ref idref="oc-fd"/>
      </symbol>
      <symbol id="sm-3bd">
         <name>__register_lock</name>
         <value>0x9ad9</value>
         <object_component_ref idref="oc-107"/>
      </symbol>
      <symbol id="sm-3be">
         <name>__nop</name>
         <value>0x9add</value>
         <object_component_ref idref="oc-107"/>
      </symbol>
      <symbol id="sm-3bf">
         <name>__register_unlock</name>
         <value>0x9ad5</value>
         <object_component_ref idref="oc-107"/>
      </symbol>
      <symbol id="sm-3cf">
         <name>__args_main</name>
         <value>0x9aa3</value>
         <object_component_ref idref="oc-fa"/>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
