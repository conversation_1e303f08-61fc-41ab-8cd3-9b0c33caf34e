/*
 * led.c
 *
 *  Created on: 2025-7-2
 *      Author: 17165
 */
#include "leds.h"

void led_ctrl(unsigned char led1,unsigned char led2,unsigned char led3,unsigned char led4)
{
	if(led1)
		GpioDataRegs.GPBSET.bit.GPIO50 = 1;
	else
		GpioDataRegs.GPBCLEAR.bit.GPIO50 = 1;

	if(led2)
		GpioDataRegs.GPBSET.bit.GPIO51 = 1;
	else
		GpioDataRegs.GPBCLEAR.bit.GPIO51 = 1;

	if(led3)
		GpioDataRegs.GPBSET.bit.GPIO52 = 1;
	else
		GpioDataRegs.GPBCLEAR.bit.GPIO52 = 1;

	if(led4)
		GpioDataRegs.GPBSET.bit.GPIO53 = 1;
	else
		GpioDataRegs.GPBCLEAR.bit.GPIO53 = 1;
}

void led_init(void)
{
	EALLOW;
	GpioCtrlRegs.GPAMUX1.bit.GPIO12 = 0;  // GPIO12
	GpioCtrlRegs.GPAMUX1.bit.GPIO13 = 0;  // GPIO13

	GpioCtrlRegs.GPADIR.bit.GPIO12 = 1;   // output
	GpioCtrlRegs.GPADIR.bit.GPIO13 = 1;   // output

	GpioCtrlRegs.GPBMUX2.bit.GPIO50 = 0;  // GPIO50
	GpioCtrlRegs.GPBMUX2.bit.GPIO51 = 0;  // GPIO51
	GpioCtrlRegs.GPBMUX2.bit.GPIO52 = 0;  // GPIO52
	GpioCtrlRegs.GPBMUX2.bit.GPIO53 = 0;  // GPIO53

    GpioCtrlRegs.GPBDIR.bit.GPIO50 = 1;   // output
    GpioCtrlRegs.GPBDIR.bit.GPIO51 = 1;   // output
    GpioCtrlRegs.GPBDIR.bit.GPIO52 = 1;   // output
    GpioCtrlRegs.GPBDIR.bit.GPIO53 = 1;   // output
    EDIS;
}





