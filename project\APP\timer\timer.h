/*
 * timer.h
 *
 *  Created on: 2025-7-2
 *      Author: 17165
 */

#ifndef TIMER_H_
#define TIMER_H_

#include "DSP2833x_Device.h"     // DSP2833x Headerfile Include File
#include "DSP2833x_Examples.h"   // DSP2833x Examples Include File

void TIM0_Init(float Freq, float Period);
interrupt void TIM0_IRQn(void);
void data_proc(void);

extern unsigned char led_flag;
extern int minute;
extern int hour;
extern int second;
extern int year;
extern int month;
extern int day;


#endif /* TIMER_H_ */
