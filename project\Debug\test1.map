******************************************************************************
             TMS320C2000 Linker PC v15.9.0                     
******************************************************************************
>> Linked Tue Jul 01 18:15:28 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00"  address: 0000941b


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
PAGE 0:
  BEGIN                 00000000   00000002  00000002  00000000  RWIX
  BOOT_RSVD             00000002   0000004e  00000000  0000004e  RWIX
  RAMM0                 00000050   000003b0  00000000  000003b0  RWIX
  RAML0                 00008000   00001000  0000001a  00000fe6  RWIX
  RAML1                 00009000   00001000  00000545  00000abb  RWIX
  RAML2                 0000a000   00001000  00000000  00001000  RWIX
  RAML3                 0000b000   00001000  00000000  00001000  RWIX
  ZONE6A                00100000   0000fc00  00000000  0000fc00  RWIX
  CSM_RSVD              0033ff80   00000076  00000000  00000076  RWIX
  CSM_PWL               0033fff8   00000008  00000000  00000008  RWIX
  ADC_CAL               00380080   00000009  00000007  00000002  RWIX
  IQTABLES              003fe000   00000b50  00000000  00000b50  RWIX
  IQTABLES2             003feb50   0000008c  00000000  0000008c  RWIX
  FPUTABLES             003febdc   000006a0  00000000  000006a0  RWIX
  BOOTROM               003ff27c   00000d44  00000000  00000d44  RWIX
  RESET                 003fffc0   00000002  00000000  00000002  RWIX

PAGE 1:
  RAMM1                 00000400   00000400  00000300  00000100  RWIX
  DEV_EMU               00000880   00000180  000000d0  000000b0  RWIX
  FLASH_REGS            00000a80   00000060  00000008  00000058  RWIX
  CSM                   00000ae0   00000010  00000010  00000000  RWIX
  ADC_MIRROR            00000b00   00000010  00000010  00000000  RWIX
  XINTF                 00000b20   00000020  0000001e  00000002  RWIX
  CPU_TIMER0            00000c00   00000008  00000008  00000000  RWIX
  CPU_TIMER1            00000c08   00000008  00000008  00000000  RWIX
  CPU_TIMER2            00000c10   00000008  00000008  00000000  RWIX
  PIE_CTRL              00000ce0   00000020  0000001a  00000006  RWIX
  PIE_VECT              00000d00   00000100  00000100  00000000  RWIX
  DMA                   00001000   00000200  000000e0  00000120  RWIX
  MCBSPA                00005000   00000040  00000025  0000001b  RWIX
  MCBSPB                00005040   00000040  00000025  0000001b  RWIX
  ECANA                 00006000   00000040  00000034  0000000c  RWIX
  ECANA_LAM             00006040   00000040  00000040  00000000  RWIX
  ECANA_MOTS            00006080   00000040  00000040  00000000  RWIX
  ECANA_MOTO            000060c0   00000040  00000040  00000000  RWIX
  ECANA_MBOX            00006100   00000100  00000100  00000000  RWIX
  ECANB                 00006200   00000040  00000034  0000000c  RWIX
  ECANB_LAM             00006240   00000040  00000040  00000000  RWIX
  ECANB_MOTS            00006280   00000040  00000040  00000000  RWIX
  ECANB_MOTO            000062c0   00000040  00000040  00000000  RWIX
  ECANB_MBOX            00006300   00000100  00000100  00000000  RWIX
  EPWM1                 00006800   00000022  00000022  00000000  RWIX
  EPWM2                 00006840   00000022  00000022  00000000  RWIX
  EPWM3                 00006880   00000022  00000022  00000000  RWIX
  EPWM4                 000068c0   00000022  00000022  00000000  RWIX
  EPWM5                 00006900   00000022  00000022  00000000  RWIX
  EPWM6                 00006940   00000022  00000022  00000000  RWIX
  ECAP1                 00006a00   00000020  00000020  00000000  RWIX
  ECAP2                 00006a20   00000020  00000020  00000000  RWIX
  ECAP3                 00006a40   00000020  00000020  00000000  RWIX
  ECAP4                 00006a60   00000020  00000020  00000000  RWIX
  ECAP5                 00006a80   00000020  00000020  00000000  RWIX
  ECAP6                 00006aa0   00000020  00000020  00000000  RWIX
  EQEP1                 00006b00   00000040  00000040  00000000  RWIX
  EQEP2                 00006b40   00000040  00000040  00000000  RWIX
  GPIOCTRL              00006f80   00000040  0000002e  00000012  RWIX
  GPIODAT               00006fc0   00000020  00000020  00000000  RWIX
  GPIOINT               00006fe0   00000020  0000000a  00000016  RWIX
  SYSTEM                00007010   00000020  00000020  00000000  RWIX
  SPIA                  00007040   00000010  00000010  00000000  RWIX
  SCIA                  00007050   00000010  00000010  00000000  RWIX
  XINTRUPT              00007070   00000010  00000010  00000000  RWIX
  ADC                   00007100   00000020  0000001e  00000002  RWIX
  SCIB                  00007750   00000010  00000010  00000000  RWIX
  SCIC                  00007770   00000010  00000010  00000000  RWIX
  I2CA                  00007900   00000040  00000022  0000001e  RWIX
  RAML4                 0000c000   00001000  00000008  00000ff8  RWIX
  RAML5                 0000d000   00001000  00000100  00000f00  RWIX
  RAML6                 0000e000   00001000  00000000  00001000  RWIX
  RAML7                 0000f000   00001000  00000000  00001000  RWIX
  ZONE7B                0020fc00   00000400  00000000  00000400  RWIX
  CSM_PWL               0033fff8   00000008  00000008  00000000  RWIX


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
codestart 
*          0    00000000    00000002     
                  00000000    00000002     DSP2833x_CodeStartBranch.obj (codestart)

.cinit     0    00008000    00000016     
                  00008000    0000000a     rts2800_fpu32.lib : exit.obj (.cinit)
                  0000800a    00000005                       : _lock.obj (.cinit:__lock)
                  0000800f    00000005                       : _lock.obj (.cinit:__unlock)
                  00008014    00000002     --HOLE-- [fill = 0]

ramfuncs   0    00008016    00000004     
                  00008016    00000004     DSP2833x_usDelay.obj (ramfuncs)

.text      0    00009000    00000545     
                  00009000    00000323     DSP2833x_DefaultIsr.obj (.text:retain)
                  00009323    000000f8     DSP2833x_SysCtrl.obj (.text)
                  0000941b    00000050     rts2800_fpu32.lib : boot.obj (.text)
                  0000946b    00000046                       : cpy_tbl.obj (.text)
                  000094b1    00000037     main.obj (.text)
                  000094e8    0000001a     rts2800_fpu32.lib : cpy_utils.obj (.text)
                  00009502    00000019                       : args_main.obj (.text)
                  0000951b    00000019                       : exit.obj (.text)
                  00009534    00000009                       : _lock.obj (.text)
                  0000953d    00000008     DSP2833x_CodeStartBranch.obj (.text)

.pinit     0    00008000    00000000     UNINITIALIZED

.adc_cal   0    00380080    00000007     NOLOAD SECTION
                  00380080    00000007     DSP2833x_ADC_cal.obj (.adc_cal)

.stack     1    00000400    00000300     UNINITIALIZED
                  00000400    00000300     --HOLE--

DevEmuRegsFile 
*          1    00000880    000000d0     UNINITIALIZED
                  00000880    000000d0     DSP2833x_GlobalVariableDefs.obj (DevEmuRegsFile)

FlashRegsFile 
*          1    00000a80    00000008     UNINITIALIZED
                  00000a80    00000008     DSP2833x_GlobalVariableDefs.obj (FlashRegsFile)

CsmRegsFile 
*          1    00000ae0    00000010     UNINITIALIZED
                  00000ae0    00000010     DSP2833x_GlobalVariableDefs.obj (CsmRegsFile)

AdcMirrorFile 
*          1    00000b00    00000010     UNINITIALIZED
                  00000b00    00000010     DSP2833x_GlobalVariableDefs.obj (AdcMirrorFile)

XintfRegsFile 
*          1    00000b20    0000001e     UNINITIALIZED
                  00000b20    0000001e     DSP2833x_GlobalVariableDefs.obj (XintfRegsFile)

CpuTimer0RegsFile 
*          1    00000c00    00000008     UNINITIALIZED
                  00000c00    00000008     DSP2833x_GlobalVariableDefs.obj (CpuTimer0RegsFile)

CpuTimer1RegsFile 
*          1    00000c08    00000008     UNINITIALIZED
                  00000c08    00000008     DSP2833x_GlobalVariableDefs.obj (CpuTimer1RegsFile)

CpuTimer2RegsFile 
*          1    00000c10    00000008     UNINITIALIZED
                  00000c10    00000008     DSP2833x_GlobalVariableDefs.obj (CpuTimer2RegsFile)

PieCtrlRegsFile 
*          1    00000ce0    0000001a     UNINITIALIZED
                  00000ce0    0000001a     DSP2833x_GlobalVariableDefs.obj (PieCtrlRegsFile)

PieVectTableFile 
*          1    00000d00    00000100     UNINITIALIZED
                  00000d00    00000100     DSP2833x_GlobalVariableDefs.obj (PieVectTableFile)

DmaRegsFile 
*          1    00001000    000000e0     UNINITIALIZED
                  00001000    000000e0     DSP2833x_GlobalVariableDefs.obj (DmaRegsFile)

McbspaRegsFile 
*          1    00005000    00000025     UNINITIALIZED
                  00005000    00000025     DSP2833x_GlobalVariableDefs.obj (McbspaRegsFile)

McbspbRegsFile 
*          1    00005040    00000025     UNINITIALIZED
                  00005040    00000025     DSP2833x_GlobalVariableDefs.obj (McbspbRegsFile)

ECanaRegsFile 
*          1    00006000    00000034     UNINITIALIZED
                  00006000    00000034     DSP2833x_GlobalVariableDefs.obj (ECanaRegsFile)

ECanaLAMRegsFile 
*          1    00006040    00000040     UNINITIALIZED
                  00006040    00000040     DSP2833x_GlobalVariableDefs.obj (ECanaLAMRegsFile)

ECanaMOTSRegsFile 
*          1    00006080    00000040     UNINITIALIZED
                  00006080    00000040     DSP2833x_GlobalVariableDefs.obj (ECanaMOTSRegsFile)

ECanaMOTORegsFile 
*          1    000060c0    00000040     UNINITIALIZED
                  000060c0    00000040     DSP2833x_GlobalVariableDefs.obj (ECanaMOTORegsFile)

ECanaMboxesFile 
*          1    00006100    00000100     UNINITIALIZED
                  00006100    00000100     DSP2833x_GlobalVariableDefs.obj (ECanaMboxesFile)

ECanbRegsFile 
*          1    00006200    00000034     UNINITIALIZED
                  00006200    00000034     DSP2833x_GlobalVariableDefs.obj (ECanbRegsFile)

ECanbLAMRegsFile 
*          1    00006240    00000040     UNINITIALIZED
                  00006240    00000040     DSP2833x_GlobalVariableDefs.obj (ECanbLAMRegsFile)

ECanbMOTSRegsFile 
*          1    00006280    00000040     UNINITIALIZED
                  00006280    00000040     DSP2833x_GlobalVariableDefs.obj (ECanbMOTSRegsFile)

ECanbMOTORegsFile 
*          1    000062c0    00000040     UNINITIALIZED
                  000062c0    00000040     DSP2833x_GlobalVariableDefs.obj (ECanbMOTORegsFile)

ECanbMboxesFile 
*          1    00006300    00000100     UNINITIALIZED
                  00006300    00000100     DSP2833x_GlobalVariableDefs.obj (ECanbMboxesFile)

EPwm1RegsFile 
*          1    00006800    00000022     UNINITIALIZED
                  00006800    00000022     DSP2833x_GlobalVariableDefs.obj (EPwm1RegsFile)

EPwm2RegsFile 
*          1    00006840    00000022     UNINITIALIZED
                  00006840    00000022     DSP2833x_GlobalVariableDefs.obj (EPwm2RegsFile)

EPwm3RegsFile 
*          1    00006880    00000022     UNINITIALIZED
                  00006880    00000022     DSP2833x_GlobalVariableDefs.obj (EPwm3RegsFile)

EPwm4RegsFile 
*          1    000068c0    00000022     UNINITIALIZED
                  000068c0    00000022     DSP2833x_GlobalVariableDefs.obj (EPwm4RegsFile)

EPwm5RegsFile 
*          1    00006900    00000022     UNINITIALIZED
                  00006900    00000022     DSP2833x_GlobalVariableDefs.obj (EPwm5RegsFile)

EPwm6RegsFile 
*          1    00006940    00000022     UNINITIALIZED
                  00006940    00000022     DSP2833x_GlobalVariableDefs.obj (EPwm6RegsFile)

ECap1RegsFile 
*          1    00006a00    00000020     UNINITIALIZED
                  00006a00    00000020     DSP2833x_GlobalVariableDefs.obj (ECap1RegsFile)

ECap2RegsFile 
*          1    00006a20    00000020     UNINITIALIZED
                  00006a20    00000020     DSP2833x_GlobalVariableDefs.obj (ECap2RegsFile)

ECap3RegsFile 
*          1    00006a40    00000020     UNINITIALIZED
                  00006a40    00000020     DSP2833x_GlobalVariableDefs.obj (ECap3RegsFile)

ECap4RegsFile 
*          1    00006a60    00000020     UNINITIALIZED
                  00006a60    00000020     DSP2833x_GlobalVariableDefs.obj (ECap4RegsFile)

ECap5RegsFile 
*          1    00006a80    00000020     UNINITIALIZED
                  00006a80    00000020     DSP2833x_GlobalVariableDefs.obj (ECap5RegsFile)

ECap6RegsFile 
*          1    00006aa0    00000020     UNINITIALIZED
                  00006aa0    00000020     DSP2833x_GlobalVariableDefs.obj (ECap6RegsFile)

EQep1RegsFile 
*          1    00006b00    00000040     UNINITIALIZED
                  00006b00    00000040     DSP2833x_GlobalVariableDefs.obj (EQep1RegsFile)

EQep2RegsFile 
*          1    00006b40    00000040     UNINITIALIZED
                  00006b40    00000040     DSP2833x_GlobalVariableDefs.obj (EQep2RegsFile)

GpioCtrlRegsFile 
*          1    00006f80    0000002e     UNINITIALIZED
                  00006f80    0000002e     DSP2833x_GlobalVariableDefs.obj (GpioCtrlRegsFile)

GpioDataRegsFile 
*          1    00006fc0    00000020     UNINITIALIZED
                  00006fc0    00000020     DSP2833x_GlobalVariableDefs.obj (GpioDataRegsFile)

GpioIntRegsFile 
*          1    00006fe0    0000000a     UNINITIALIZED
                  00006fe0    0000000a     DSP2833x_GlobalVariableDefs.obj (GpioIntRegsFile)

SysCtrlRegsFile 
*          1    00007010    00000020     UNINITIALIZED
                  00007010    00000020     DSP2833x_GlobalVariableDefs.obj (SysCtrlRegsFile)

SpiaRegsFile 
*          1    00007040    00000010     UNINITIALIZED
                  00007040    00000010     DSP2833x_GlobalVariableDefs.obj (SpiaRegsFile)

SciaRegsFile 
*          1    00007050    00000010     UNINITIALIZED
                  00007050    00000010     DSP2833x_GlobalVariableDefs.obj (SciaRegsFile)

XIntruptRegsFile 
*          1    00007070    00000010     UNINITIALIZED
                  00007070    00000010     DSP2833x_GlobalVariableDefs.obj (XIntruptRegsFile)

AdcRegsFile 
*          1    00007100    0000001e     UNINITIALIZED
                  00007100    0000001e     DSP2833x_GlobalVariableDefs.obj (AdcRegsFile)

ScibRegsFile 
*          1    00007750    00000010     UNINITIALIZED
                  00007750    00000010     DSP2833x_GlobalVariableDefs.obj (ScibRegsFile)

ScicRegsFile 
*          1    00007770    00000010     UNINITIALIZED
                  00007770    00000010     DSP2833x_GlobalVariableDefs.obj (ScicRegsFile)

I2caRegsFile 
*          1    00007900    00000022     UNINITIALIZED
                  00007900    00000022     DSP2833x_GlobalVariableDefs.obj (I2caRegsFile)

.ebss      1    0000c000    00000008     UNINITIALIZED
                  0000c000    00000004     rts2800_fpu32.lib : exit.obj (.ebss)
                  0000c004    00000002                       : _lock.obj (.ebss:__lock)
                  0000c006    00000002                       : _lock.obj (.ebss:__unlock)

.econst    1    0000d000    00000100     
                  0000d000    00000100     DSP2833x_PieVect.obj (.econst)

.reset     0    003fffc0    00000002     DSECT
                  003fffc0    00000002     rts2800_fpu32.lib : boot.obj (.reset)

csm_rsvd   0    0033ff80    00000000     DSECT

csmpasswds 
*          0    0033fff8    00000000     DSECT

CsmPwlFile 
*          1    0033fff8    00000008     UNINITIALIZED
                  0033fff8    00000008     DSP2833x_GlobalVariableDefs.obj (CsmPwlFile)

MODULE SUMMARY

       Module                            code   ro data   rw data
       ------                            ----   -------   -------
    .\User\
       main.obj                          55     0         0      
    +--+---------------------------------+------+---------+---------+
       Total:                            55     0         0      
                                                                 
    .\new project\
       DSP2833x_GlobalVariableDefs.obj   0      0         2678   
       DSP2833x_DefaultIsr.obj           803    0         0      
       DSP2833x_PieVect.obj              0      0         256    
       DSP2833x_SysCtrl.obj              248    0         0      
       DSP2833x_CodeStartBranch.obj      10     0         0      
       DSP2833x_ADC_cal.obj              7      0         0      
       DSP2833x_usDelay.obj              4      0         0      
    +--+---------------------------------+------+---------+---------+
       Total:                            1072   0         2934   
                                                                 
    C:\ti\lib\rts2800_fpu32.lib
       boot.obj                          80     0         0      
       cpy_tbl.obj                       70     0         0      
       exit.obj                          25     0         14     
       cpy_utils.obj                     26     0         0      
       args_main.obj                     25     0         0      
       _lock.obj                         9      0         14     
    +--+---------------------------------+------+---------+---------+
       Total:                            235    0         28     
                                                                 
       Stack:                            0      0         768    
    +--+---------------------------------+------+---------+---------+
       Grand Total:                      1362   0         3730   


GLOBAL DATA SYMBOLS: SORTED BY DATA PAGE

address     data page           name
--------    ----------------    ----
00000400      10 (00000400)     __stack

00000880      22 (00000880)     _DevEmuRegs

00000a80      2a (00000a80)     _FlashRegs

00000ae0      2b (00000ac0)     _CsmRegs

00000b00      2c (00000b00)     _AdcMirror
00000b20      2c (00000b00)     _XintfRegs

00000c00      30 (00000c00)     _CpuTimer0Regs
00000c08      30 (00000c00)     _CpuTimer1Regs
00000c10      30 (00000c00)     _CpuTimer2Regs

00000ce0      33 (00000cc0)     _PieCtrlRegs

00000d00      34 (00000d00)     _PieVectTable

00001000      40 (00001000)     _DmaRegs

00005000     140 (00005000)     _McbspaRegs

00005040     141 (00005040)     _McbspbRegs

00006000     180 (00006000)     _ECanaRegs

00006040     181 (00006040)     _ECanaLAMRegs

00006080     182 (00006080)     _ECanaMOTSRegs

000060c0     183 (000060c0)     _ECanaMOTORegs

00006100     184 (00006100)     _ECanaMboxes

00006200     188 (00006200)     _ECanbRegs

00006240     189 (00006240)     _ECanbLAMRegs

00006280     18a (00006280)     _ECanbMOTSRegs

000062c0     18b (000062c0)     _ECanbMOTORegs

00006300     18c (00006300)     _ECanbMboxes

00006800     1a0 (00006800)     _EPwm1Regs

00006840     1a1 (00006840)     _EPwm2Regs

00006880     1a2 (00006880)     _EPwm3Regs

000068c0     1a3 (000068c0)     _EPwm4Regs

00006900     1a4 (00006900)     _EPwm5Regs

00006940     1a5 (00006940)     _EPwm6Regs

00006a00     1a8 (00006a00)     _ECap1Regs
00006a20     1a8 (00006a00)     _ECap2Regs

00006a40     1a9 (00006a40)     _ECap3Regs
00006a60     1a9 (00006a40)     _ECap4Regs

00006a80     1aa (00006a80)     _ECap5Regs
00006aa0     1aa (00006a80)     _ECap6Regs

00006b00     1ac (00006b00)     _EQep1Regs

00006b40     1ad (00006b40)     _EQep2Regs

00006f80     1be (00006f80)     _GpioCtrlRegs

00006fc0     1bf (00006fc0)     _GpioDataRegs
00006fe0     1bf (00006fc0)     _GpioIntRegs

00007010     1c0 (00007000)     _SysCtrlRegs

00007040     1c1 (00007040)     _SpiaRegs
00007050     1c1 (00007040)     _SciaRegs
00007070     1c1 (00007040)     _XIntruptRegs

00007100     1c4 (00007100)     _AdcRegs

00007750     1dd (00007740)     _ScibRegs
00007770     1dd (00007740)     _ScicRegs

00007900     1e4 (00007900)     _I2caRegs

0000c000     300 (0000c000)     ___TI_cleanup_ptr
0000c002     300 (0000c000)     ___TI_dtors_ptr
0000c004     300 (0000c000)     __lock
0000c006     300 (0000c000)     __unlock

0000d000     340 (0000d000)     _PieVectTableInit

0033fff8    cfff (0033ffc0)     _CsmPwl


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

page  address   name                 
----  -------   ----                 
0     00009000  .text                
0     0000951b  C$$EXIT              
0     000090e6  _ADCINT_ISR          
0     00380080  _ADC_cal             
1     00000b00  _AdcMirror           
1     00007100  _AdcRegs             
1     00000c00  _CpuTimer0Regs       
1     00000c08  _CpuTimer1Regs       
1     00000c10  _CpuTimer2Regs       
1     0033fff8  _CsmPwl              
1     00000ae0  _CsmRegs             
0     000093ea  _CsmUnlock           
0     00009014  _DATALOG_ISR         
0     00009208  _DINTCH1_ISR         
0     00009212  _DINTCH2_ISR         
0     0000921c  _DINTCH3_ISR         
0     00009226  _DINTCH4_ISR         
0     00009230  _DINTCH5_ISR         
0     0000923a  _DINTCH6_ISR         
0     00008016  _DSP28x_usDelay      
1     00000880  _DevEmuRegs          
0     00009336  _DisableDog          
1     00001000  _DmaRegs             
0     00009294  _ECAN0INTA_ISR       
0     000092a8  _ECAN0INTB_ISR       
0     0000929e  _ECAN1INTA_ISR       
0     000092b2  _ECAN1INTB_ISR       
0     0000917c  _ECAP1_INT_ISR       
0     00009186  _ECAP2_INT_ISR       
0     00009190  _ECAP3_INT_ISR       
0     0000919a  _ECAP4_INT_ISR       
0     000091a4  _ECAP5_INT_ISR       
0     000091ae  _ECAP6_INT_ISR       
1     00006040  _ECanaLAMRegs        
1     000060c0  _ECanaMOTORegs       
1     00006080  _ECanaMOTSRegs       
1     00006100  _ECanaMboxes         
1     00006000  _ECanaRegs           
1     00006240  _ECanbLAMRegs        
1     000062c0  _ECanbMOTORegs       
1     00006280  _ECanbMOTSRegs       
1     00006300  _ECanbMboxes         
1     00006200  _ECanbRegs           
1     00006a00  _ECap1Regs           
1     00006a20  _ECap2Regs           
1     00006a40  _ECap3Regs           
1     00006a60  _ECap4Regs           
1     00006a80  _ECap5Regs           
1     00006aa0  _ECap6Regs           
0     00009302  _EMPTY_ISR           
0     00009028  _EMUINT_ISR          
0     00009140  _EPWM1_INT_ISR       
0     00009104  _EPWM1_TZINT_ISR     
0     0000914a  _EPWM2_INT_ISR       
0     0000910e  _EPWM2_TZINT_ISR     
0     00009154  _EPWM3_INT_ISR       
0     00009118  _EPWM3_TZINT_ISR     
0     0000915e  _EPWM4_INT_ISR       
0     00009122  _EPWM4_TZINT_ISR     
0     00009168  _EPWM5_INT_ISR       
0     0000912c  _EPWM5_TZINT_ISR     
0     00009172  _EPWM6_INT_ISR       
0     00009136  _EPWM6_TZINT_ISR     
1     00006800  _EPwm1Regs           
1     00006840  _EPwm2Regs           
1     00006880  _EPwm3Regs           
1     000068c0  _EPwm4Regs           
1     00006900  _EPwm5Regs           
1     00006940  _EPwm6Regs           
0     000091b8  _EQEP1_INT_ISR       
0     000091c2  _EQEP2_INT_ISR       
1     00006b00  _EQep1Regs           
1     00006b40  _EQep2Regs           
1     00000a80  _FlashRegs           
1     00006f80  _GpioCtrlRegs        
1     00006fc0  _GpioDataRegs        
1     00006fe0  _GpioIntRegs         
0     00009244  _I2CINT1A_ISR        
0     0000924e  _I2CINT2A_ISR        
1     00007900  _I2caRegs            
0     0000903c  _ILLEGAL_ISR         
0     00009000  _INT13_ISR           
0     0000900a  _INT14_ISR           
0     0000938f  _InitPeripheralClocks
0     0000933e  _InitPll             
0     00009323  _InitSysCtrl         
0     000094c9  _LED_Init            
0     000092f8  _LUF_ISR             
0     000092ee  _LVF_ISR             
0     000091f4  _MRINTA_ISR          
0     000091e0  _MRINTB_ISR          
0     000091fe  _MXINTA_ISR          
0     000091ea  _MXINTB_ISR          
1     00005000  _McbspaRegs          
1     00005040  _McbspbRegs          
0     00009032  _NMI_ISR             
0     0000930f  _PIE_RESERVED        
1     00000ce0  _PieCtrlRegs         
1     00000d00  _PieVectTable        
1     0000d000  _PieVectTableInit    
0     0000901e  _RTOSINT_ISR         
0     0000926c  _SCIRXINTA_ISR       
0     00009280  _SCIRXINTB_ISR       
0     00009258  _SCIRXINTC_ISR       
0     00009276  _SCITXINTA_ISR       
0     0000928a  _SCITXINTB_ISR       
0     00009262  _SCITXINTC_ISR       
0     000090be  _SEQ1INT_ISR         
0     000090c8  _SEQ2INT_ISR         
0     000091cc  _SPIRXINTA_ISR       
0     000091d6  _SPITXINTA_ISR       
1     00007050  _SciaRegs            
1     00007750  _ScibRegs            
1     00007770  _ScicRegs            
0     0000932c  _ServiceDog          
1     00007040  _SpiaRegs            
1     00007010  _SysCtrlRegs         
0     000090f0  _TINT0_ISR           
0     000090a0  _USER10_ISR          
0     000090aa  _USER11_ISR          
0     000090b4  _USER12_ISR          
0     00009046  _USER1_ISR           
0     00009050  _USER2_ISR           
0     0000905a  _USER3_ISR           
0     00009064  _USER4_ISR           
0     0000906e  _USER5_ISR           
0     00009078  _USER6_ISR           
0     00009082  _USER7_ISR           
0     0000908c  _USER8_ISR           
0     00009096  _USER9_ISR           
0     000090fa  _WAKEINT_ISR         
0     000090d2  _XINT1_ISR           
0     000090dc  _XINT2_ISR           
0     000092bc  _XINT3_ISR           
0     000092c6  _XINT4_ISR           
0     000092d0  _XINT5_ISR           
0     000092da  _XINT6_ISR           
0     000092e4  _XINT7_ISR           
1     00007070  _XIntruptRegs        
1     00000b20  _XintfRegs           
1     00000700  __STACK_END          
abs   00000300  __STACK_SIZE         
1     0000c000  ___TI_cleanup_ptr    
1     0000c002  ___TI_dtors_ptr      
abs   ffffffff  ___binit__           
abs   ffffffff  ___c_args__          
0     00008000  ___cinit__           
0     00009545  ___etext__           
abs   ffffffff  ___pinit__           
0     00009000  ___text__            
0     00009502  __args_main          
1     0000c004  __lock               
0     0000953c  __nop                
0     00009538  __register_lock      
0     00009534  __register_unlock    
1     00000400  __stack              
1     0000c006  __unlock             
0     0000951b  _abort               
0     0000941b  _c_int00             
0     0000946b  _copy_in             
0     000094e8  _ddcopy              
0     000094b1  _delay               
0     000094e8  _dpcopy              
0     0000951d  _exit                
0     000094dd  _main                
0     000094e8  _pdcopy              
0     000094e8  _ppcopy              
0     00009319  _rsvd_ISR            
abs   ffffffff  binit                
0     00008000  cinit                
0     00000000  code_start           
0     00009545  etext                
abs   ffffffff  pinit                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

page  address   name                 
----  -------   ----                 
0     00000000  code_start           
0     00008000  ___cinit__           
0     00008000  cinit                
0     00008016  _DSP28x_usDelay      
0     00009000  .text                
0     00009000  _INT13_ISR           
0     00009000  ___text__            
0     0000900a  _INT14_ISR           
0     00009014  _DATALOG_ISR         
0     0000901e  _RTOSINT_ISR         
0     00009028  _EMUINT_ISR          
0     00009032  _NMI_ISR             
0     0000903c  _ILLEGAL_ISR         
0     00009046  _USER1_ISR           
0     00009050  _USER2_ISR           
0     0000905a  _USER3_ISR           
0     00009064  _USER4_ISR           
0     0000906e  _USER5_ISR           
0     00009078  _USER6_ISR           
0     00009082  _USER7_ISR           
0     0000908c  _USER8_ISR           
0     00009096  _USER9_ISR           
0     000090a0  _USER10_ISR          
0     000090aa  _USER11_ISR          
0     000090b4  _USER12_ISR          
0     000090be  _SEQ1INT_ISR         
0     000090c8  _SEQ2INT_ISR         
0     000090d2  _XINT1_ISR           
0     000090dc  _XINT2_ISR           
0     000090e6  _ADCINT_ISR          
0     000090f0  _TINT0_ISR           
0     000090fa  _WAKEINT_ISR         
0     00009104  _EPWM1_TZINT_ISR     
0     0000910e  _EPWM2_TZINT_ISR     
0     00009118  _EPWM3_TZINT_ISR     
0     00009122  _EPWM4_TZINT_ISR     
0     0000912c  _EPWM5_TZINT_ISR     
0     00009136  _EPWM6_TZINT_ISR     
0     00009140  _EPWM1_INT_ISR       
0     0000914a  _EPWM2_INT_ISR       
0     00009154  _EPWM3_INT_ISR       
0     0000915e  _EPWM4_INT_ISR       
0     00009168  _EPWM5_INT_ISR       
0     00009172  _EPWM6_INT_ISR       
0     0000917c  _ECAP1_INT_ISR       
0     00009186  _ECAP2_INT_ISR       
0     00009190  _ECAP3_INT_ISR       
0     0000919a  _ECAP4_INT_ISR       
0     000091a4  _ECAP5_INT_ISR       
0     000091ae  _ECAP6_INT_ISR       
0     000091b8  _EQEP1_INT_ISR       
0     000091c2  _EQEP2_INT_ISR       
0     000091cc  _SPIRXINTA_ISR       
0     000091d6  _SPITXINTA_ISR       
0     000091e0  _MRINTB_ISR          
0     000091ea  _MXINTB_ISR          
0     000091f4  _MRINTA_ISR          
0     000091fe  _MXINTA_ISR          
0     00009208  _DINTCH1_ISR         
0     00009212  _DINTCH2_ISR         
0     0000921c  _DINTCH3_ISR         
0     00009226  _DINTCH4_ISR         
0     00009230  _DINTCH5_ISR         
0     0000923a  _DINTCH6_ISR         
0     00009244  _I2CINT1A_ISR        
0     0000924e  _I2CINT2A_ISR        
0     00009258  _SCIRXINTC_ISR       
0     00009262  _SCITXINTC_ISR       
0     0000926c  _SCIRXINTA_ISR       
0     00009276  _SCITXINTA_ISR       
0     00009280  _SCIRXINTB_ISR       
0     0000928a  _SCITXINTB_ISR       
0     00009294  _ECAN0INTA_ISR       
0     0000929e  _ECAN1INTA_ISR       
0     000092a8  _ECAN0INTB_ISR       
0     000092b2  _ECAN1INTB_ISR       
0     000092bc  _XINT3_ISR           
0     000092c6  _XINT4_ISR           
0     000092d0  _XINT5_ISR           
0     000092da  _XINT6_ISR           
0     000092e4  _XINT7_ISR           
0     000092ee  _LVF_ISR             
0     000092f8  _LUF_ISR             
0     00009302  _EMPTY_ISR           
0     0000930f  _PIE_RESERVED        
0     00009319  _rsvd_ISR            
0     00009323  _InitSysCtrl         
0     0000932c  _ServiceDog          
0     00009336  _DisableDog          
0     0000933e  _InitPll             
0     0000938f  _InitPeripheralClocks
0     000093ea  _CsmUnlock           
0     0000941b  _c_int00             
0     0000946b  _copy_in             
0     000094b1  _delay               
0     000094c9  _LED_Init            
0     000094dd  _main                
0     000094e8  _ddcopy              
0     000094e8  _dpcopy              
0     000094e8  _pdcopy              
0     000094e8  _ppcopy              
0     00009502  __args_main          
0     0000951b  C$$EXIT              
0     0000951b  _abort               
0     0000951d  _exit                
0     00009534  __register_unlock    
0     00009538  __register_lock      
0     0000953c  __nop                
0     00009545  ___etext__           
0     00009545  etext                
0     00380080  _ADC_cal             
1     00000400  __stack              
1     00000700  __STACK_END          
1     00000880  _DevEmuRegs          
1     00000a80  _FlashRegs           
1     00000ae0  _CsmRegs             
1     00000b00  _AdcMirror           
1     00000b20  _XintfRegs           
1     00000c00  _CpuTimer0Regs       
1     00000c08  _CpuTimer1Regs       
1     00000c10  _CpuTimer2Regs       
1     00000ce0  _PieCtrlRegs         
1     00000d00  _PieVectTable        
1     00001000  _DmaRegs             
1     00005000  _McbspaRegs          
1     00005040  _McbspbRegs          
1     00006000  _ECanaRegs           
1     00006040  _ECanaLAMRegs        
1     00006080  _ECanaMOTSRegs       
1     000060c0  _ECanaMOTORegs       
1     00006100  _ECanaMboxes         
1     00006200  _ECanbRegs           
1     00006240  _ECanbLAMRegs        
1     00006280  _ECanbMOTSRegs       
1     000062c0  _ECanbMOTORegs       
1     00006300  _ECanbMboxes         
1     00006800  _EPwm1Regs           
1     00006840  _EPwm2Regs           
1     00006880  _EPwm3Regs           
1     000068c0  _EPwm4Regs           
1     00006900  _EPwm5Regs           
1     00006940  _EPwm6Regs           
1     00006a00  _ECap1Regs           
1     00006a20  _ECap2Regs           
1     00006a40  _ECap3Regs           
1     00006a60  _ECap4Regs           
1     00006a80  _ECap5Regs           
1     00006aa0  _ECap6Regs           
1     00006b00  _EQep1Regs           
1     00006b40  _EQep2Regs           
1     00006f80  _GpioCtrlRegs        
1     00006fc0  _GpioDataRegs        
1     00006fe0  _GpioIntRegs         
1     00007010  _SysCtrlRegs         
1     00007040  _SpiaRegs            
1     00007050  _SciaRegs            
1     00007070  _XIntruptRegs        
1     00007100  _AdcRegs             
1     00007750  _ScibRegs            
1     00007770  _ScicRegs            
1     00007900  _I2caRegs            
1     0000c000  ___TI_cleanup_ptr    
1     0000c002  ___TI_dtors_ptr      
1     0000c004  __lock               
1     0000c006  __unlock             
1     0000d000  _PieVectTableInit    
1     0033fff8  _CsmPwl              
abs   00000300  __STACK_SIZE         
abs   ffffffff  ___binit__           
abs   ffffffff  ___c_args__          
abs   ffffffff  ___pinit__           
abs   ffffffff  binit                
abs   ffffffff  pinit                

[173 symbols]
