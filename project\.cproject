<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?>

<cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule configRelations="2" moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="com.ti.ccstudio.buildDefinitions.C2000.Debug.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.ti.ccstudio.buildDefinitions.C2000.Debug.**********" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="com.ti.ccstudio.binaryparser.CoffParser" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.ti.ccstudio.errorparser.CoffErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.ti.ccstudio.errorparser.LinkErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.ti.ccstudio.errorparser.AsmErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="out" artifactName="${ProjName}" buildProperties="" cleanCommand="${CG_CLEAN_CMD}" description="" id="com.ti.ccstudio.buildDefinitions.C2000.Debug.**********" name="Debug" parent="com.ti.ccstudio.buildDefinitions.C2000.Debug">
					<folderInfo id="com.ti.ccstudio.buildDefinitions.C2000.Debug.**********." name="/" resourcePath="">
						<toolChain id="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.DebugToolchain.1569711472" name="TI Build Tools" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.DebugToolchain" targetTool="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.linkerDebug.145960574">
							<option id="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.504279037" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS" valueType="stringList">
								<listOptionValue builtIn="false" value="DEVICE_CONFIGURATION_ID=TMS320C28XX.TMS320F28335"/>
								<listOptionValue builtIn="false" value="OUTPUT_FORMAT=COFF"/>
								<listOptionValue builtIn="false" value="CCS_MBS_VERSION=5.5.0"/>
								<listOptionValue builtIn="false" value="LINKER_COMMAND_FILE=28335_RAM_lnk.cmd"/>
								<listOptionValue builtIn="false" value="RUNTIME_SUPPORT_LIBRARY=libc.a"/>
								<listOptionValue builtIn="false" value="OUTPUT_TYPE=executable"/>
							</option>
							<option id="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.213742809" name="Compiler version" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION" value="15.9.0" valueType="string"/>
							<targetPlatform id="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.targetPlatformDebug.859840423" name="Platform" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.targetPlatformDebug"/>
							<builder buildPath="${BuildDirectory}" id="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.builderDebug.1129386166" keepEnvironmentInBuildfile="false" name="GNU Make" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.builderDebug"/>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.compilerDebug.698272466" name="C2000 Compiler" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.compilerDebug">
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.LARGE_MEMORY_MODEL.1423443069" name="Option deprecated, set by default (--large_memory_model, -ml)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.LARGE_MEMORY_MODEL" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.UNIFIED_MEMORY.2048704766" name="Unified memory (--unified_memory, -mt)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.UNIFIED_MEMORY" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.SILICON_VERSION.1109968967" name="Processor version (--silicon_version, -v)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.SILICON_VERSION" value="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.SILICON_VERSION.28" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.FLOAT_SUPPORT.752144645" name="Specify floating point support (--float_support)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.FLOAT_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.FLOAT_SUPPORT.fpu32" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.DEBUGGING_MODEL.211135870" name="Debugging model" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.DEBUGGING_MODEL" value="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.DEBUGGING_MODEL.SYMDEBUG__DWARF" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.DIAG_WARNING.120545204" name="Treat diagnostic &lt;id&gt; as warning (--diag_warning, -pdsw)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.DIAG_WARNING" valueType="stringList">
									<listOptionValue builtIn="false" value="225"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.DISPLAY_ERROR_NUMBER.1025804715" name="Emit diagnostic identifier numbers (--display_error_number, -pden)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.DISPLAY_ERROR_NUMBER" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.DIAG_WRAP.881989755" name="Wrap diagnostic messages (--diag_wrap)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.DIAG_WRAP" value="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.DIAG_WRAP.off" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.INCLUDE_PATH.1826970908" name="Add dir to #include search path (--include_path, -I)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.INCLUDE_PATH" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${CG_TOOL_ROOT}/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;C:\ti\workspace\sys_clock\project\APP\clock_set&quot;"/>
									<listOptionValue builtIn="false" value="&quot;C:\ti\workspace\sys_clock\project\APP\lcd&quot;"/>
									<listOptionValue builtIn="false" value="&quot;C:\ti\workspace\sys_clock\project\APP\timer&quot;"/>
									<listOptionValue builtIn="false" value="&quot;C:\ti\workspace\sys_clock\project\APP\key&quot;"/>
									<listOptionValue builtIn="false" value="&quot;C:\ti\workspace\sys_clock\project\APP\led_code&quot;"/>
									<listOptionValue builtIn="false" value="&quot;C:\ti\workspace\DSP2833x_Libraries\DSP2833x_common\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;C:\ti\workspace\DSP2833x_Libraries\DSP2833x_headers\include&quot;"/>
								</option>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compiler.inputType__C_SRCS.795191410" name="C Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compiler.inputType__C_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compiler.inputType__CPP_SRCS.944104900" name="C++ Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compiler.inputType__CPP_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compiler.inputType__ASM_SRCS.775195450" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compiler.inputType__ASM_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compiler.inputType__ASM2_SRCS.2007439015" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compiler.inputType__ASM2_SRCS"/>
							</tool>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.linkerDebug.145960574" name="C2000 Linker" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.linkerDebug">
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.STACK_SIZE.207168309" name="Set C system stack size (--stack_size, -stack)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.STACK_SIZE" value="0x300" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.OUTPUT_FILE.531780854" name="Specify output file name (--output_file, -o)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.OUTPUT_FILE" value="&quot;${ProjName}.out&quot;" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.MAP_FILE.601976522" name="Link information (map) listed into &lt;file&gt; (--map_file, -m)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.MAP_FILE" value="&quot;${ProjName}.map&quot;" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.XML_LINK_INFO.762035952" name="Detailed link information data-base into &lt;file&gt; (--xml_link_info, -xml_link_info)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.XML_LINK_INFO" value="&quot;${ProjName}_linkInfo.xml&quot;" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.DISPLAY_ERROR_NUMBER.616146668" name="Emit diagnostic identifier numbers (--display_error_number)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.DISPLAY_ERROR_NUMBER" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.DIAG_WRAP.1187805581" name="Wrap diagnostic messages (--diag_wrap)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.DIAG_WRAP" value="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.DIAG_WRAP.off" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.SEARCH_PATH.1519648316" name="Add &lt;dir&gt; to library search path (--search_path, -i)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.SEARCH_PATH" valueType="libPaths">
									<listOptionValue builtIn="false" value="&quot;${CG_TOOL_ROOT}/lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${CG_TOOL_ROOT}/include&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.LIBRARY.385610211" name="Include library file or command file as input (--library, -l)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.LIBRARY" valueType="libs">
									<listOptionValue builtIn="false" value="&quot;libc.a&quot;"/>
								</option>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_15.9.exeLinker.inputType__CMD_SRCS.417574394" name="Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.exeLinker.inputType__CMD_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_15.9.exeLinker.inputType__CMD2_SRCS.1881112074" name="Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.exeLinker.inputType__CMD2_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_15.9.exeLinker.inputType__GEN_CMDS.**********" name="Generated Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.exeLinker.inputType__GEN_CMDS"/>
							</tool>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="com.ti.ccstudio.buildDefinitions.C2000.Release.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.ti.ccstudio.buildDefinitions.C2000.Release.**********" moduleId="org.eclipse.cdt.core.settings" name="Release">
				<externalSettings/>
				<extensions>
					<extension id="com.ti.ccstudio.binaryparser.CoffParser" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.ti.ccstudio.errorparser.CoffErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.ti.ccstudio.errorparser.LinkErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.ti.ccstudio.errorparser.AsmErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="out" artifactName="${ProjName}" buildProperties="" cleanCommand="${CG_CLEAN_CMD}" description="" id="com.ti.ccstudio.buildDefinitions.C2000.Release.**********" name="Release" parent="com.ti.ccstudio.buildDefinitions.C2000.Release">
					<folderInfo id="com.ti.ccstudio.buildDefinitions.C2000.Release.**********." name="/" resourcePath="">
						<toolChain id="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.ReleaseToolchain.456560387" name="TI Build Tools" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.ReleaseToolchain" targetTool="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.linkerRelease.144033184">
							<option id="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.1978020197" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS" valueType="stringList">
								<listOptionValue builtIn="false" value="DEVICE_CONFIGURATION_ID=TMS320C28XX.TMS320F28335"/>
								<listOptionValue builtIn="false" value="OUTPUT_FORMAT=COFF"/>
								<listOptionValue builtIn="false" value="CCS_MBS_VERSION=5.5.0"/>
								<listOptionValue builtIn="false" value="LINKER_COMMAND_FILE=28335_RAM_lnk.cmd"/>
								<listOptionValue builtIn="false" value="RUNTIME_SUPPORT_LIBRARY=libc.a"/>
								<listOptionValue builtIn="false" value="OUTPUT_TYPE=executable"/>
							</option>
							<option id="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.191387611" name="Compiler version" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION" value="15.9.0" valueType="string"/>
							<targetPlatform id="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.targetPlatformRelease.1375821902" name="Platform" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.targetPlatformRelease"/>
							<builder buildPath="${BuildDirectory}" id="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.builderRelease.774404437" keepEnvironmentInBuildfile="false" name="GNU Make" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.builderRelease"/>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.compilerRelease.924805803" name="C2000 Compiler" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.compilerRelease">
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.LARGE_MEMORY_MODEL.1952206780" name="Option deprecated, set by default (--large_memory_model, -ml)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.LARGE_MEMORY_MODEL" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.UNIFIED_MEMORY.1860806715" name="Unified memory (--unified_memory, -mt)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.UNIFIED_MEMORY" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.SILICON_VERSION.2041331712" name="Processor version (--silicon_version, -v)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.SILICON_VERSION" value="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.SILICON_VERSION.28" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.FLOAT_SUPPORT.1601621585" name="Specify floating point support (--float_support)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.FLOAT_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.FLOAT_SUPPORT.fpu32" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.DIAG_WARNING.80423661" name="Treat diagnostic &lt;id&gt; as warning (--diag_warning, -pdsw)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.DIAG_WARNING" valueType="stringList">
									<listOptionValue builtIn="false" value="225"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.DISPLAY_ERROR_NUMBER.83650061" name="Emit diagnostic identifier numbers (--display_error_number, -pden)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.DISPLAY_ERROR_NUMBER" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.DIAG_WRAP.1640941337" name="Wrap diagnostic messages (--diag_wrap)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.DIAG_WRAP" value="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.DIAG_WRAP.off" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.INCLUDE_PATH.1448023331" name="Add dir to #include search path (--include_path, -I)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compilerID.INCLUDE_PATH" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${CG_TOOL_ROOT}/include&quot;"/>
								</option>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compiler.inputType__C_SRCS.1766155530" name="C Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compiler.inputType__C_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compiler.inputType__CPP_SRCS.272084869" name="C++ Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compiler.inputType__CPP_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compiler.inputType__ASM_SRCS.1858559242" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compiler.inputType__ASM_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_15.9.compiler.inputType__ASM2_SRCS.920161174" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.compiler.inputType__ASM2_SRCS"/>
							</tool>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.linkerRelease.144033184" name="C2000 Linker" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.exe.linkerRelease">
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.STACK_SIZE.949500597" name="Set C system stack size (--stack_size, -stack)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.STACK_SIZE" value="0x300" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.OUTPUT_FILE.717666154" name="Specify output file name (--output_file, -o)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.OUTPUT_FILE" value="&quot;${ProjName}.out&quot;" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.MAP_FILE.298641513" name="Link information (map) listed into &lt;file&gt; (--map_file, -m)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.MAP_FILE" value="&quot;${ProjName}.map&quot;" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.XML_LINK_INFO.2129560605" name="Detailed link information data-base into &lt;file&gt; (--xml_link_info, -xml_link_info)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.XML_LINK_INFO" value="&quot;${ProjName}_linkInfo.xml&quot;" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.DISPLAY_ERROR_NUMBER.420464766" name="Emit diagnostic identifier numbers (--display_error_number)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.DISPLAY_ERROR_NUMBER" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.DIAG_WRAP.57292186" name="Wrap diagnostic messages (--diag_wrap)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.DIAG_WRAP" value="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.DIAG_WRAP.off" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.SEARCH_PATH.418927758" name="Add &lt;dir&gt; to library search path (--search_path, -i)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.SEARCH_PATH" valueType="libPaths">
									<listOptionValue builtIn="false" value="&quot;${CG_TOOL_ROOT}/lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${CG_TOOL_ROOT}/include&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.LIBRARY.837578233" name="Include library file or command file as input (--library, -l)" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.linkerID.LIBRARY" valueType="libs">
									<listOptionValue builtIn="false" value="&quot;libc.a&quot;"/>
								</option>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_15.9.exeLinker.inputType__CMD_SRCS.1533247952" name="Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.exeLinker.inputType__CMD_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_15.9.exeLinker.inputType__CMD2_SRCS.690012128" name="Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.exeLinker.inputType__CMD2_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_15.9.exeLinker.inputType__GEN_CMDS.**********" name="Generated Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_15.9.exeLinker.inputType__GEN_CMDS"/>
							</tool>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="test1.com.ti.ccstudio.buildDefinitions.C2000.ProjectType.**********" name="C2000" projectType="com.ti.ccstudio.buildDefinitions.C2000.ProjectType"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration"/>
	<storageModule moduleId="org.eclipse.cdt.core.language.mapping">
		<project-mappings>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.asmSource" language="com.ti.ccstudio.core.TIASMLanguage"/>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cHeader" language="com.ti.ccstudio.core.TIGCCLanguage"/>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cSource" language="com.ti.ccstudio.core.TIGCCLanguage"/>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cxxHeader" language="com.ti.ccstudio.core.TIGPPLanguage"/>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cxxSource" language="com.ti.ccstudio.core.TIGPPLanguage"/>
		</project-mappings>
	</storageModule>
	<storageModule moduleId="null.endianPreference"/>
	<storageModule moduleId="cpuFamily"/>
</cproject>
