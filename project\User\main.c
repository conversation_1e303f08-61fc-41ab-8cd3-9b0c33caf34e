/*
 * timer.c
 *
 *  Created on: 2025-7-2
 *      Author: 17165
 */
#include "timer.h"

unsigned char led_flag=0;//闪烁标志位
int hour=23,minute=59,second=55;  //时分秒 变量单独在定时器中定义
int year=2004,month=12,day=31;    //初始化年月日

interrupt void TIM0_IRQn(void);

void TIM0_Init(float Freq, float Period)
{
	EALLOW;
	SysCtrlRegs.PCLKCR3.bit.CPUTIMER0ENCLK = 1; // CPU Timer 0
	EDIS;

	//设置定时器0的中断入口地址为中断向量表的INT0
	EALLOW;
	PieVectTable.TINT0 = &TIM0_IRQn;
	EDIS;

	//指向定时器0的寄存器地址
	CpuTimer0.RegsAddr = &CpuTimer0Regs;
	//设置定时器0的周期寄存器值
	CpuTimer0Regs.PRD.all  = 0xFFFFFFFF;
	//设置定时器预定标计数器值为0
	CpuTimer0Regs.TPR.all  = 0;
	CpuTimer0Regs.TPRH.all = 0;
	//确保定时器0为停止状态
	CpuTimer0Regs.TCR.bit.TSS = 1;
	//重载使能
	CpuTimer0Regs.TCR.bit.TRB = 1;
	// Reset interrupt counters:
	CpuTimer0.InterruptCount = 0;

	ConfigCpuTimer(&CpuTimer0, Freq, Period);

	//开始定时器功能
	CpuTimer0Regs.TCR.bit.TSS=0;
	//开启CPU第一组中断并使能第一组中断的第7个小中断，即定时器0
	IER |= M_INT1;
	PieCtrlRegs.PIEIER1.bit.INTx7 = 1;
	//使能总中断
	EINT;
	ERTM;

}

interrupt void TIM0_IRQn(void)
{
	static Uint16 cnt=0;

		cnt++;
		if(cnt==100)
		{
			cnt=0;
			led_flag^=1;
			second++;
		}
	PieCtrlRegs.PIEACK.bit.ACK1=1;
}

void data_proc(void)
{
	if(second==60)
	{
		second=0;
		minute++;
	}
	if(minute==60)
	{
		minute=0;
		hour++;
	}
	if(hour==24)
	{
		hour=0;
		day++;
	}
	switch(month)
	{
	    case 1:
		   if(day==32)
		   {
			   day=1;
			   month++;
		   }
		break;
	    case 2://特殊月份
	    	if((year%400==0)||(year%4==0&&year%100!=0))
	    		 {if(day>=30)
	    		 {
	    			 day=1;
	    			 month++;
	    		 }}
	    	else
	    		 {if(day>=29)
	    		 {
	    			 day=1;
	    			 month++;
	    		 }}
	    break;
	    case 3:
	    	if(day==32)
	    	 {
	  			 day=1;
  				 month++;
	    	 }
	    break;
	    case 4:
	    	 if(day==31)
	    	 {
	    		 day=1;
	    		 month++;
	    	 }
	    break;
	    case 5:
             if(day==32)
             {
            	 day=1;
            	 month++;
             }
	    break;
	    case 6:
	    	 if(day==31)
	    	 {
	    		 day=1;
	    		 month++;
	    	 }
	    break;
	    case 7:
	         if(day==32)
	         {
	        	 day=1;
	        	 month++;
	         }
	    break;
	    case 8:
	    	  if(day==32)
	    	  {
	    		  day=1;
	    		  month++;
	    	  }
	    break;
	    case 9:
	    	  if(day==31)
	    	  {
	    		  day=1;
	    		  month++;
	    	  }
	    break;
	    case 10:
	    	  if(day==32)
	    	  {
	    		  day=1;
	    		  month++;
	    	  }
	    break;
	    case 11:
	          if(day==31)
	          {
	        	  day=1;
	        	  month++;
	          }
	    break;
	    case 12:
	    	  if(day==32)
	    	  {
	    		  day=1;
	    		  month++;
	    	  }
	    break;
	}
	if(month==13)
	{
		month=1;
		year++;
	}
}

