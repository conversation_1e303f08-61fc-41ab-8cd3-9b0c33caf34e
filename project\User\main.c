/*
 * main.c
 *
 *  Created on: 2018-3-21
 *      Author: Administrator
 */
#include "DSP2833x_Device.h"     // DSP2833x Headerfile Include File
#include "DSP2833x_Examples.h"   // DSP2833x Examples Include File
#include "leds.h"    //使用led底层
#include "key.h"     //key底层
#include "timer.h"    //调用定时器
#include "lcd1602.h"  //LCD使用
#include "clock.h"    //将数字转化为字符串
unsigned char key_val=0;
unsigned char clock_mode=0;//按键使用     lcd显示模式
int time[6];//用于放置参数
unsigned char time_index=0;//数组索引变量
char a[4];char b[2];char c[2];   //年月日所对应的字符串
char d[2];char e[2];char f[2];   //对应时分秒
char str1[11];   //LCD第一行
char str2[9];    //LCD第二行
/*******************************************************************************
* 函 数 名         : LED_Init
* 函数功能		   : LED初始化函数
* 输    入         : 无
* 输    出         : 无
*******************************************************************************/
/*void LED_Init(void)
{
	EALLOW;//关闭写保护
	SysCtrlRegs.PCLKCR3.bit.GPIOINENCLK = 1;    // 开启GPIO时钟

	//LED1端口配置
	GpioCtrlRegs.GPCMUX1.bit.GPIO68=0;//设置为通用GPIO功能
	GpioCtrlRegs.GPCDIR.bit.GPIO68=1;//设置GPIO方向为输出
	GpioCtrlRegs.GPCPUD.bit.GPIO68=0;//使能GPIO上拉电阻

	GpioDataRegs.GPCSET.bit.GPIO68=1;//设置GPIO输出高电平

	EDIS;//开启写保护
}*/
/*******************************************************************************
* 函 数 名         : main
* 函数功能		   : 主函数
* 输    入         : 无
* 输    出         : 无
*******************************************************************************/
void key_proc(void)
{

   //key_val=key_read();
   switch(key_val)
   {
     case 1:
    	if(clock_mode==0)//时间流转模式按下
    	{   //将实时时间赋给设置值 存放在数组中
    		time[0]=year;
    		time[1]=month;
    		time[2]=day;
    		time[3]=hour;
    		time[4]=minute;
    		time[5]=second;
    		clock_mode^=1;//按一下key1进入设置模式
    		time_index=0;//索引置0，从年开始调整
    	}
    	else if(time_index<6&&clock_mode==1)//判断条件
    	{
    		time_index++;
    	}
    	else if(time_index==6&&clock_mode==1)
    	{   //时间设置模式按下
    		year=time[0];
      		month=time[1];
      		day=time[2];
      		hour=time[3];
    		minute=time[4];
    		second=time[5];
     		clock_mode^=1;   //再按一下保留设置时间并恢复流转
    	}
     break;
     case 2:
    	 if(clock_mode)//按键2仅在设置时间模式生效
    	 {
    		 switch(time_index)//根据不同的参数，界定范围
    		 {
    		  case 0://年份
    			 if(time[time_index]<9999) time[time_index]++;
    		  break;
    		  case 1://月份
    			 if(time[time_index]<12) time[time_index]++;
    		  break;
    		  case 2://日数
    			 if(time[time_index]<31) time[time_index]++;
    		  break;
    		  case 3://小时
    			 if(time[time_index]<23) time[time_index]++;
    		  break;
    		  case 4://分钟
    			 if(time[time_index]<59) time[time_index]++;
    		  break;
    		  case 5://秒数
    			 if(time[time_index]<59) time[time_index]++;
    		  break;
    		 }
    	 }
     break;
     case 3:
    	 if(clock_mode)//按键3仅在设置时间模式生效
    	 {
    		 switch(time_index)//根据不同的参数，界定范围
    		 {
    		  case 0://年份
    			 if(time[time_index]>0) time[time_index]--;
    		  break;
    		  case 1://月份
    			 if(time[time_index]>1) time[time_index]--;
    		  break;
    		  case 2://日数
    			 if(time[time_index]>1) time[time_index]--;
    		  break;
    		  case 3://小时
    			 if(time[time_index]>0) time[time_index]--;
    		  break;
    		  case 4://分钟
    			 if(time[time_index]>0) time[time_index]--;
    		  break;
    		  case 5://秒数
    			 if(time[time_index]>0) time[time_index]--;
    		  break;
    		 }
    	 }
     break;
   }
}

void clock_proc(void)
{
	if(clock_mode)//时间设置模式
	{
		num_to_str(time[0],a);num_to_str(time[1],b);num_to_str(time[2],c);//将年月日数字化为字符串
		str_add1(a,b,c,str1);   //拼接字符串
		num_to_str(time[3],d);num_to_str(time[4],e);num_to_str(time[5],f);//将时分秒数字化为字符串
		str_add2(d,e,f,str2);   //拼接字符串
	}
	else  if(clock_mode==0)       //正常工作模式
	{
		num_to_str(year,a);num_to_str(month,b);num_to_str(day,c);//将年月日数字化为字符串
		str_add1(a,b,c,str1);   //拼接字符串
		num_to_str(hour,d);num_to_str(minute,e);num_to_str(second,f);//将时分秒数字化为字符串
		str_add2(d,e,f,str2);   //拼接字符串
	}
	LCD_write_command(0x80);	//第一行数据指针地址
	 show(str1);                //在LCD上显示
	LCD_write_command(0xc0);	//第二行数据指针的地址
	 show(str2);                //在LCD上显示
}

void main()
{
	InitSysCtrl();//系统时钟初始化，默认已开启F28335所有外设时钟
	InitPieCtrl();   //关外部中断
	IER = 0x0000;
	IFR = 0x0000;
	InitPieVectTable();  //防止在初始化中被打断
    TIM0_Init(150,10000);//此处为10ms中断一次
	led_init();        //led初始化
	key_init();        //key初始化
	Init_Port();       //LCD接口配置
	LCD_init();        //LCD初始化
	while(1)
	{
		key_proc();
		led_ctrl(led_flag,0,0,1);   //控制led，表明不同的模式
		data_proc();                //计算时间
		clock_proc();               //显示LCD内容
	}

}




