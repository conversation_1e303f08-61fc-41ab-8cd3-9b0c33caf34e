# FIXED

new\ project/DSP2833x_CpuTimers.obj: ../new\ project/DSP2833x_CpuTimers.c
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Device.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Adc.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_DevEmu.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_CpuTimers.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_ECan.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_ECap.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_DMA.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_EPwm.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_EQep.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Gpio.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_I2c.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_McBSP.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_PieCtrl.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_PieVect.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Spi.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Sci.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_SysCtrl.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_XIntrupt.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Xintf.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_Examples.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_GlobalPrototypes.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_ePwm_defines.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_Dma_defines.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_I2C_defines.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/LED.h
new\ project/DSP2833x_CpuTimers.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_DefaultISR.h

../new\ project/DSP2833x_CpuTimers.c: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Device.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Adc.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_DevEmu.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_CpuTimers.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_ECan.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_ECap.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_DMA.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_EPwm.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_EQep.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Gpio.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_I2c.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_McBSP.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_PieCtrl.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_PieVect.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Spi.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Sci.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_SysCtrl.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_XIntrupt.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Xintf.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_Examples.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_GlobalPrototypes.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_ePwm_defines.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_Dma_defines.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_I2C_defines.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/LED.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_DefaultISR.h: 
