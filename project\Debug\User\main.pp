# FIXED

User/main.obj: ../User/main.c
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Device.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Adc.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_DevEmu.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_CpuTimers.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_ECan.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_ECap.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_DMA.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_EPwm.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_EQep.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Gpio.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_I2c.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_McBSP.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_PieCtrl.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_PieVect.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Spi.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Sci.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_SysCtrl.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_XIntrupt.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Xintf.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_Examples.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_GlobalPrototypes.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_ePwm_defines.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_Dma_defines.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_I2C_defines.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/LED.h
User/main.obj: C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_DefaultISR.h
User/main.obj: C:/ti/workspace/sys_clock/project/APP/led_code/leds.h
User/main.obj: C:/ti/workspace/sys_clock/project/APP/key/key.h
User/main.obj: C:/ti/workspace/sys_clock/project/APP/timer/timer.h
User/main.obj: C:/ti/workspace/sys_clock/project/APP/lcd/lcd1602.h
User/main.obj: C:/ti/workspace/sys_clock/project/APP/clock_set/clock.h

../User/main.c: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Device.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Adc.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_DevEmu.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_CpuTimers.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_ECan.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_ECap.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_DMA.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_EPwm.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_EQep.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Gpio.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_I2c.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_McBSP.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_PieCtrl.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_PieVect.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Spi.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Sci.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_SysCtrl.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_XIntrupt.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_headers/include/DSP2833x_Xintf.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_Examples.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_GlobalPrototypes.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_ePwm_defines.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_Dma_defines.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_I2C_defines.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/LED.h: 
C:/ti/workspace/DSP2833x_Libraries/DSP2833x_common/include/DSP2833x_DefaultISR.h: 
C:/ti/workspace/sys_clock/project/APP/led_code/leds.h: 
C:/ti/workspace/sys_clock/project/APP/key/key.h: 
C:/ti/workspace/sys_clock/project/APP/timer/timer.h: 
C:/ti/workspace/sys_clock/project/APP/lcd/lcd1602.h: 
C:/ti/workspace/sys_clock/project/APP/clock_set/clock.h: 
