<?xml version="1.0"?>
<link_info>
   <banner>TMS320C2000 Linker PC v15.9.0.STS</banner>
   <copyright>Copyright (c) 1996-2015 Texas Instruments Incorporated</copyright>
   <link_time>0x6863b540</link_time>
   <link_errors>0x0</link_errors>
   <output_file>test1.out</output_file>
   <entry_point>
      <name>_c_int00</name>
      <address>0x941b</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-2">
         <path>.\User\</path>
         <kind>object</kind>
         <file>main.obj</file>
         <name>main.obj</name>
      </input_file>
      <input_file id="fl-3">
         <path>.\new project\</path>
         <kind>object</kind>
         <file>DSP2833x_usDelay.obj</file>
         <name>DSP2833x_usDelay.obj</name>
      </input_file>
      <input_file id="fl-4">
         <path>.\new project\</path>
         <kind>object</kind>
         <file>DSP2833x_SysCtrl.obj</file>
         <name>DSP2833x_SysCtrl.obj</name>
      </input_file>
      <input_file id="fl-5">
         <path>.\new project\</path>
         <kind>object</kind>
         <file>DSP2833x_PieVect.obj</file>
         <name>DSP2833x_PieVect.obj</name>
      </input_file>
      <input_file id="fl-6">
         <path>.\new project\</path>
         <kind>object</kind>
         <file>DSP2833x_PieCtrl.obj</file>
         <name>DSP2833x_PieCtrl.obj</name>
      </input_file>
      <input_file id="fl-7">
         <path>.\new project\</path>
         <kind>object</kind>
         <file>DSP2833x_Gpio.obj</file>
         <name>DSP2833x_Gpio.obj</name>
      </input_file>
      <input_file id="fl-8">
         <path>.\new project\</path>
         <kind>object</kind>
         <file>DSP2833x_GlobalVariableDefs.obj</file>
         <name>DSP2833x_GlobalVariableDefs.obj</name>
      </input_file>
      <input_file id="fl-9">
         <path>.\new project\</path>
         <kind>object</kind>
         <file>DSP2833x_DefaultIsr.obj</file>
         <name>DSP2833x_DefaultIsr.obj</name>
      </input_file>
      <input_file id="fl-a">
         <path>.\new project\</path>
         <kind>object</kind>
         <file>DSP2833x_CodeStartBranch.obj</file>
         <name>DSP2833x_CodeStartBranch.obj</name>
      </input_file>
      <input_file id="fl-b">
         <path>.\new project\</path>
         <kind>object</kind>
         <file>DSP2833x_ADC_cal.obj</file>
         <name>DSP2833x_ADC_cal.obj</name>
      </input_file>
      <input_file id="fl-17">
         <path>C:\ti\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>boot.obj</name>
      </input_file>
      <input_file id="fl-18">
         <path>C:\ti\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>cpy_tbl.obj</name>
      </input_file>
      <input_file id="fl-19">
         <path>C:\ti\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>cpy_utils.obj</name>
      </input_file>
      <input_file id="fl-1a">
         <path>C:\ti\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>exit.obj</name>
      </input_file>
      <input_file id="fl-1b">
         <path>C:\ti\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>_lock.obj</name>
      </input_file>
      <input_file id="fl-1c">
         <path>C:\ti\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>args_main.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-a5">
         <name>codestart</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-51">
         <name>ramfuncs</name>
         <load_address>0x8016</load_address>
         <run_address>0x8016</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.text:retain</name>
         <load_address>0x9000</load_address>
         <run_address>0x9000</run_address>
         <size>0x323</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.text</name>
         <load_address>0x9323</load_address>
         <run_address>0x9323</run_address>
         <size>0xf8</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.text</name>
         <load_address>0x941b</load_address>
         <run_address>0x941b</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.text</name>
         <load_address>0x946b</load_address>
         <run_address>0x946b</run_address>
         <size>0x46</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.text</name>
         <load_address>0x94b1</load_address>
         <run_address>0x94b1</run_address>
         <size>0x37</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.text</name>
         <load_address>0x94e8</load_address>
         <run_address>0x94e8</run_address>
         <size>0x1a</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.text</name>
         <load_address>0x9502</load_address>
         <run_address>0x9502</run_address>
         <size>0x19</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.text</name>
         <load_address>0x951b</load_address>
         <run_address>0x951b</run_address>
         <size>0x19</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.text</name>
         <load_address>0x9534</load_address>
         <run_address>0x9534</run_address>
         <size>0x9</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.text</name>
         <load_address>0x953d</load_address>
         <run_address>0x953d</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.cinit</name>
         <load_address>0x8000</load_address>
         <run_address>0x8000</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.cinit:__lock</name>
         <load_address>0x800a</load_address>
         <run_address>0x800a</run_address>
         <size>0x5</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.cinit:__unlock</name>
         <load_address>0x800f</load_address>
         <run_address>0x800f</run_address>
         <size>0x5</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-af">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <run_address>0x400</run_address>
         <size>0x0</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <run_address>0x400</run_address>
         <size>0x0</size>
      </object_component>
      <object_component id="oc-c1">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0xc000</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.ebss:__unlock</name>
         <uninitialized>true</uninitialized>
         <run_address>0xc006</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.ebss:__lock</name>
         <uninitialized>true</uninitialized>
         <run_address>0xc004</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.econst</name>
         <load_address>0xd000</load_address>
         <run_address>0xd000</run_address>
         <size>0x100</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.reset</name>
         <load_address>0x3fffc0</load_address>
         <run_address>0x3fffc0</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.adc_cal</name>
         <load_address>0x380080</load_address>
         <run_address>0x380080</run_address>
         <size>0x7</size>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-9c">
         <name>PieVectTableFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xd00</run_address>
         <size>0x100</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-98">
         <name>DevEmuRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x880</run_address>
         <size>0xd0</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-6e">
         <name>FlashRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xa80</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-78">
         <name>CsmRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xae0</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-77">
         <name>AdcMirrorFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xb00</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-7b">
         <name>XintfRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xb20</run_address>
         <size>0x1e</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-6f">
         <name>CpuTimer0RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xc00</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-6d">
         <name>CpuTimer1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xc08</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-6c">
         <name>CpuTimer2RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xc10</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-79">
         <name>PieCtrlRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xce0</run_address>
         <size>0x1a</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-99">
         <name>DmaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x1000</run_address>
         <size>0xe0</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-8b">
         <name>McbspaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5000</run_address>
         <size>0x25</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-8c">
         <name>McbspbRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5040</run_address>
         <size>0x25</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-8f">
         <name>ECanaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6000</run_address>
         <size>0x34</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-92">
         <name>ECanaLAMRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6040</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-9b">
         <name>ECanaMboxesFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6100</run_address>
         <size>0x100</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-93">
         <name>ECanaMOTSRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6080</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-90">
         <name>ECanaMOTORegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x60c0</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-8e">
         <name>ECanbRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6200</run_address>
         <size>0x34</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-96">
         <name>ECanbLAMRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6240</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-9a">
         <name>ECanbMboxesFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6300</run_address>
         <size>0x100</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-91">
         <name>ECanbMOTSRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6280</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-94">
         <name>ECanbMOTORegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x62c0</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-86">
         <name>EPwm1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6800</run_address>
         <size>0x22</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-85">
         <name>EPwm2RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6840</run_address>
         <size>0x22</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-87">
         <name>EPwm3RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6880</run_address>
         <size>0x22</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-89">
         <name>EPwm4RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x68c0</run_address>
         <size>0x22</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-88">
         <name>EPwm5RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6900</run_address>
         <size>0x22</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-8a">
         <name>EPwm6RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6940</run_address>
         <size>0x22</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-82">
         <name>ECap1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6a00</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-7e">
         <name>ECap2RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6a20</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-7f">
         <name>ECap3RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6a40</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-7c">
         <name>ECap4RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6a60</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-7d">
         <name>ECap5RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6a80</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-83">
         <name>ECap6RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6aa0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-97">
         <name>EQep1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6b00</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-95">
         <name>EQep2RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6b40</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-8d">
         <name>GpioCtrlRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6f80</run_address>
         <size>0x2e</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-80">
         <name>GpioDataRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6fc0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-71">
         <name>GpioIntRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6fe0</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-81">
         <name>SysCtrlRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7010</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-74">
         <name>SpiaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7040</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-75">
         <name>SciaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7050</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-76">
         <name>XIntruptRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7070</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-7a">
         <name>AdcRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7100</run_address>
         <size>0x1e</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-73">
         <name>ScibRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7750</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-72">
         <name>ScicRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7770</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-84">
         <name>I2caRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7900</run_address>
         <size>0x22</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-70">
         <name>CsmPwlFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x33fff8</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2931</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_info</name>
         <load_address>0x2931</load_address>
         <run_address>0x2931</run_address>
         <size>0xee</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-56">
         <name>.debug_info</name>
         <load_address>0x2a1f</load_address>
         <run_address>0x2a1f</run_address>
         <size>0x1fe2</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.debug_info</name>
         <load_address>0x4a01</load_address>
         <run_address>0x4a01</run_address>
         <size>0x2211</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_info</name>
         <load_address>0x6c12</load_address>
         <run_address>0x6c12</run_address>
         <size>0xa77</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_info</name>
         <load_address>0x7689</load_address>
         <run_address>0x7689</run_address>
         <size>0x1b95</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_info</name>
         <load_address>0x921e</load_address>
         <run_address>0x921e</run_address>
         <size>0x13216</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_info</name>
         <load_address>0x1c434</load_address>
         <run_address>0x1c434</run_address>
         <size>0x27e6</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.debug_info</name>
         <load_address>0x1ec1a</load_address>
         <run_address>0x1ec1a</run_address>
         <size>0x131</size>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_info</name>
         <load_address>0x1ed4b</load_address>
         <run_address>0x1ed4b</run_address>
         <size>0xee</size>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_info</name>
         <load_address>0x1ee39</load_address>
         <run_address>0x1ee39</run_address>
         <size>0x133</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_info</name>
         <load_address>0x1ef6c</load_address>
         <run_address>0x1ef6c</run_address>
         <size>0x621</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_info</name>
         <load_address>0x1f58d</load_address>
         <run_address>0x1f58d</run_address>
         <size>0x10c</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_info</name>
         <load_address>0x1f699</load_address>
         <run_address>0x1f699</run_address>
         <size>0x4d1</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_info</name>
         <load_address>0x1fb6a</load_address>
         <run_address>0x1fb6a</run_address>
         <size>0x4fb</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_info</name>
         <load_address>0x20065</load_address>
         <run_address>0x20065</run_address>
         <size>0x4c8</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.debug_info</name>
         <load_address>0x2052d</load_address>
         <run_address>0x2052d</run_address>
         <size>0x8f</size>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xd1</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_frame</name>
         <load_address>0xd1</load_address>
         <run_address>0xd1</run_address>
         <size>0x118</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_frame</name>
         <load_address>0x1e9</load_address>
         <run_address>0x1e9</run_address>
         <size>0x8c</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_frame</name>
         <load_address>0x275</load_address>
         <run_address>0x275</run_address>
         <size>0x8c</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_frame</name>
         <load_address>0x301</load_address>
         <run_address>0x301</run_address>
         <size>0x8c</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_frame</name>
         <load_address>0x38d</load_address>
         <run_address>0x38d</run_address>
         <size>0xc28</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_frame</name>
         <load_address>0xfb5</load_address>
         <run_address>0xfb5</run_address>
         <size>0xba</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_frame</name>
         <load_address>0x106f</load_address>
         <run_address>0x106f</run_address>
         <size>0xbf</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_frame</name>
         <load_address>0x112e</load_address>
         <run_address>0x112e</run_address>
         <size>0xcb</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_frame</name>
         <load_address>0x11f9</load_address>
         <run_address>0x11f9</run_address>
         <size>0xa1</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x94</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_line</name>
         <load_address>0x94</load_address>
         <run_address>0x94</run_address>
         <size>0x54</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_line</name>
         <load_address>0xe8</load_address>
         <run_address>0xe8</run_address>
         <size>0x151</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_line</name>
         <load_address>0x239</load_address>
         <run_address>0x239</run_address>
         <size>0x42</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_line</name>
         <load_address>0x27b</load_address>
         <run_address>0x27b</run_address>
         <size>0x42</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_line</name>
         <load_address>0x2bd</load_address>
         <run_address>0x2bd</run_address>
         <size>0x3f</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_line</name>
         <load_address>0x2fc</load_address>
         <run_address>0x2fc</run_address>
         <size>0x76e</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_line</name>
         <load_address>0xa6a</load_address>
         <run_address>0xa6a</run_address>
         <size>0x6d</size>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_line</name>
         <load_address>0xad7</load_address>
         <run_address>0xad7</run_address>
         <size>0x55</size>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.debug_line</name>
         <load_address>0xb2c</load_address>
         <run_address>0xb2c</run_address>
         <size>0x7a</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_line</name>
         <load_address>0xba6</load_address>
         <run_address>0xba6</run_address>
         <size>0x6a</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_line</name>
         <load_address>0xc10</load_address>
         <run_address>0xc10</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.debug_line</name>
         <load_address>0xc60</load_address>
         <run_address>0xc60</run_address>
         <size>0x65</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_line</name>
         <load_address>0xcc5</load_address>
         <run_address>0xcc5</run_address>
         <size>0x5e</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_line</name>
         <load_address>0xd23</load_address>
         <run_address>0xd23</run_address>
         <size>0x55</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x106</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_abbrev</name>
         <load_address>0x106</load_address>
         <run_address>0x106</run_address>
         <size>0x2f</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_abbrev</name>
         <load_address>0x135</load_address>
         <run_address>0x135</run_address>
         <size>0x17b</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_abbrev</name>
         <load_address>0x2b0</load_address>
         <run_address>0x2b0</run_address>
         <size>0x116</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_abbrev</name>
         <load_address>0x3c6</load_address>
         <run_address>0x3c6</run_address>
         <size>0xc8</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-69">
         <name>.debug_abbrev</name>
         <load_address>0x48e</load_address>
         <run_address>0x48e</run_address>
         <size>0xdc</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_abbrev</name>
         <load_address>0x56a</load_address>
         <run_address>0x56a</run_address>
         <size>0xb7</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.debug_abbrev</name>
         <load_address>0x621</load_address>
         <run_address>0x621</run_address>
         <size>0xde</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_abbrev</name>
         <load_address>0x6ff</load_address>
         <run_address>0x6ff</run_address>
         <size>0x21</size>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_abbrev</name>
         <load_address>0x720</load_address>
         <run_address>0x720</run_address>
         <size>0x2f</size>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.debug_abbrev</name>
         <load_address>0x74f</load_address>
         <run_address>0x74f</run_address>
         <size>0x46</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_abbrev</name>
         <load_address>0x795</load_address>
         <run_address>0x795</run_address>
         <size>0xfa</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_abbrev</name>
         <load_address>0x88f</load_address>
         <run_address>0x88f</run_address>
         <size>0x32</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_abbrev</name>
         <load_address>0x8c1</load_address>
         <run_address>0x8c1</run_address>
         <size>0x117</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_abbrev</name>
         <load_address>0x9d8</load_address>
         <run_address>0x9d8</run_address>
         <size>0xb2</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_abbrev</name>
         <load_address>0xa8a</load_address>
         <run_address>0xa8a</run_address>
         <size>0xfe</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.debug_abbrev</name>
         <load_address>0xb88</load_address>
         <run_address>0xb88</run_address>
         <size>0xf</size>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_aranges</name>
         <load_address>0x30</load_address>
         <run_address>0x30</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5a">
         <name>.debug_aranges</name>
         <load_address>0x50</load_address>
         <run_address>0x50</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_aranges</name>
         <load_address>0x98</load_address>
         <run_address>0x98</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_aranges</name>
         <load_address>0xb0</load_address>
         <run_address>0xb0</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.debug_aranges</name>
         <load_address>0xc8</load_address>
         <run_address>0xc8</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_aranges</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x298</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_aranges</name>
         <load_address>0x378</load_address>
         <run_address>0x378</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_aranges</name>
         <load_address>0x3a0</load_address>
         <run_address>0x3a0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_aranges</name>
         <load_address>0x3c0</load_address>
         <run_address>0x3c0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_aranges</name>
         <load_address>0x3e0</load_address>
         <run_address>0x3e0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_aranges</name>
         <load_address>0x400</load_address>
         <run_address>0x400</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_aranges</name>
         <load_address>0x420</load_address>
         <run_address>0x420</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_aranges</name>
         <load_address>0x448</load_address>
         <run_address>0x448</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_aranges</name>
         <load_address>0x478</load_address>
         <run_address>0x478</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>codestart</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-a5"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>ramfuncs</name>
         <load_address>0x8016</load_address>
         <run_address>0x8016</run_address>
         <size>0x4</size>
         <contents>
            <object_component_ref idref="oc-51"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.text</name>
         <load_address>0x9000</load_address>
         <run_address>0x9000</run_address>
         <size>0x545</size>
         <contents>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-a4"/>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x8000</load_address>
         <run_address>0x8000</run_address>
         <size>0x16</size>
         <contents>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-d8"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.switch</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x400</run_address>
         <size>0x300</size>
         <contents>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-12d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.ebss</name>
         <run_address>0xc000</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-d4"/>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.econst</name>
         <load_address>0xd000</load_address>
         <run_address>0xd000</run_address>
         <size>0x100</size>
         <contents>
            <object_component_ref idref="oc-5c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.esysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>IQmath</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>IQmathTables</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>IQmathTables2</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>FPUmathTables</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>DMARAML4</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>DMARAML5</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>DMARAML6</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>DMARAML7</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14" display="no" color="cyan">
         <name>ZONE7DATA</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-15" display="no" color="cyan">
         <name>.reset</name>
         <load_address>0x3fffc0</load_address>
         <run_address>0x3fffc0</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-b0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-16" display="no" color="cyan">
         <name>csm_rsvd</name>
         <run_address>0x33ff80</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-17" display="no" color="cyan">
         <name>csmpasswds</name>
         <run_address>0x33fff8</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-18" display="no" color="cyan">
         <name>.adc_cal</name>
         <load_address>0x380080</load_address>
         <run_address>0x380080</run_address>
         <size>0x7</size>
         <contents>
            <object_component_ref idref="oc-aa"/>
         </contents>
      </logical_group>
      <logical_group id="lg-19" display="no" color="cyan">
         <name>PieVectTableFile</name>
         <run_address>0xd00</run_address>
         <size>0x100</size>
         <contents>
            <object_component_ref idref="oc-9c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1a" display="no" color="cyan">
         <name>DevEmuRegsFile</name>
         <run_address>0x880</run_address>
         <size>0xd0</size>
         <contents>
            <object_component_ref idref="oc-98"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1b" display="no" color="cyan">
         <name>FlashRegsFile</name>
         <run_address>0xa80</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-6e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1c" display="no" color="cyan">
         <name>CsmRegsFile</name>
         <run_address>0xae0</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-78"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1d" display="no" color="cyan">
         <name>AdcMirrorFile</name>
         <run_address>0xb00</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-77"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1e" display="no" color="cyan">
         <name>XintfRegsFile</name>
         <run_address>0xb20</run_address>
         <size>0x1e</size>
         <contents>
            <object_component_ref idref="oc-7b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1f" display="no" color="cyan">
         <name>CpuTimer0RegsFile</name>
         <run_address>0xc00</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-6f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-20" display="no" color="cyan">
         <name>CpuTimer1RegsFile</name>
         <run_address>0xc08</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-6d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-21" display="no" color="cyan">
         <name>CpuTimer2RegsFile</name>
         <run_address>0xc10</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-6c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-22" display="no" color="cyan">
         <name>PieCtrlRegsFile</name>
         <run_address>0xce0</run_address>
         <size>0x1a</size>
         <contents>
            <object_component_ref idref="oc-79"/>
         </contents>
      </logical_group>
      <logical_group id="lg-23" display="no" color="cyan">
         <name>DmaRegsFile</name>
         <run_address>0x1000</run_address>
         <size>0xe0</size>
         <contents>
            <object_component_ref idref="oc-99"/>
         </contents>
      </logical_group>
      <logical_group id="lg-24" display="no" color="cyan">
         <name>McbspaRegsFile</name>
         <run_address>0x5000</run_address>
         <size>0x25</size>
         <contents>
            <object_component_ref idref="oc-8b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-25" display="no" color="cyan">
         <name>McbspbRegsFile</name>
         <run_address>0x5040</run_address>
         <size>0x25</size>
         <contents>
            <object_component_ref idref="oc-8c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-26" display="no" color="cyan">
         <name>ECanaRegsFile</name>
         <run_address>0x6000</run_address>
         <size>0x34</size>
         <contents>
            <object_component_ref idref="oc-8f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-27" display="no" color="cyan">
         <name>ECanaLAMRegsFile</name>
         <run_address>0x6040</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-92"/>
         </contents>
      </logical_group>
      <logical_group id="lg-28" display="no" color="cyan">
         <name>ECanaMboxesFile</name>
         <run_address>0x6100</run_address>
         <size>0x100</size>
         <contents>
            <object_component_ref idref="oc-9b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-29" display="no" color="cyan">
         <name>ECanaMOTSRegsFile</name>
         <run_address>0x6080</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-93"/>
         </contents>
      </logical_group>
      <logical_group id="lg-2a" display="no" color="cyan">
         <name>ECanaMOTORegsFile</name>
         <run_address>0x60c0</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-90"/>
         </contents>
      </logical_group>
      <logical_group id="lg-2b" display="no" color="cyan">
         <name>ECanbRegsFile</name>
         <run_address>0x6200</run_address>
         <size>0x34</size>
         <contents>
            <object_component_ref idref="oc-8e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-2c" display="no" color="cyan">
         <name>ECanbLAMRegsFile</name>
         <run_address>0x6240</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-96"/>
         </contents>
      </logical_group>
      <logical_group id="lg-2d" display="no" color="cyan">
         <name>ECanbMboxesFile</name>
         <run_address>0x6300</run_address>
         <size>0x100</size>
         <contents>
            <object_component_ref idref="oc-9a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-2e" display="no" color="cyan">
         <name>ECanbMOTSRegsFile</name>
         <run_address>0x6280</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-91"/>
         </contents>
      </logical_group>
      <logical_group id="lg-2f" display="no" color="cyan">
         <name>ECanbMOTORegsFile</name>
         <run_address>0x62c0</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-94"/>
         </contents>
      </logical_group>
      <logical_group id="lg-30" display="no" color="cyan">
         <name>EPwm1RegsFile</name>
         <run_address>0x6800</run_address>
         <size>0x22</size>
         <contents>
            <object_component_ref idref="oc-86"/>
         </contents>
      </logical_group>
      <logical_group id="lg-31" display="no" color="cyan">
         <name>EPwm2RegsFile</name>
         <run_address>0x6840</run_address>
         <size>0x22</size>
         <contents>
            <object_component_ref idref="oc-85"/>
         </contents>
      </logical_group>
      <logical_group id="lg-32" display="no" color="cyan">
         <name>EPwm3RegsFile</name>
         <run_address>0x6880</run_address>
         <size>0x22</size>
         <contents>
            <object_component_ref idref="oc-87"/>
         </contents>
      </logical_group>
      <logical_group id="lg-33" display="no" color="cyan">
         <name>EPwm4RegsFile</name>
         <run_address>0x68c0</run_address>
         <size>0x22</size>
         <contents>
            <object_component_ref idref="oc-89"/>
         </contents>
      </logical_group>
      <logical_group id="lg-34" display="no" color="cyan">
         <name>EPwm5RegsFile</name>
         <run_address>0x6900</run_address>
         <size>0x22</size>
         <contents>
            <object_component_ref idref="oc-88"/>
         </contents>
      </logical_group>
      <logical_group id="lg-35" display="no" color="cyan">
         <name>EPwm6RegsFile</name>
         <run_address>0x6940</run_address>
         <size>0x22</size>
         <contents>
            <object_component_ref idref="oc-8a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-36" display="no" color="cyan">
         <name>ECap1RegsFile</name>
         <run_address>0x6a00</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-82"/>
         </contents>
      </logical_group>
      <logical_group id="lg-37" display="no" color="cyan">
         <name>ECap2RegsFile</name>
         <run_address>0x6a20</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-7e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-38" display="no" color="cyan">
         <name>ECap3RegsFile</name>
         <run_address>0x6a40</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-7f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-39" display="no" color="cyan">
         <name>ECap4RegsFile</name>
         <run_address>0x6a60</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-7c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3a" display="no" color="cyan">
         <name>ECap5RegsFile</name>
         <run_address>0x6a80</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-7d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3b" display="no" color="cyan">
         <name>ECap6RegsFile</name>
         <run_address>0x6aa0</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-83"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3c" display="no" color="cyan">
         <name>EQep1RegsFile</name>
         <run_address>0x6b00</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-97"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3d" display="no" color="cyan">
         <name>EQep2RegsFile</name>
         <run_address>0x6b40</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-95"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3e" display="no" color="cyan">
         <name>GpioCtrlRegsFile</name>
         <run_address>0x6f80</run_address>
         <size>0x2e</size>
         <contents>
            <object_component_ref idref="oc-8d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3f" display="no" color="cyan">
         <name>GpioDataRegsFile</name>
         <run_address>0x6fc0</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-80"/>
         </contents>
      </logical_group>
      <logical_group id="lg-40" display="no" color="cyan">
         <name>GpioIntRegsFile</name>
         <run_address>0x6fe0</run_address>
         <size>0xa</size>
         <contents>
            <object_component_ref idref="oc-71"/>
         </contents>
      </logical_group>
      <logical_group id="lg-41" display="no" color="cyan">
         <name>SysCtrlRegsFile</name>
         <run_address>0x7010</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-81"/>
         </contents>
      </logical_group>
      <logical_group id="lg-42" display="no" color="cyan">
         <name>SpiaRegsFile</name>
         <run_address>0x7040</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-74"/>
         </contents>
      </logical_group>
      <logical_group id="lg-43" display="no" color="cyan">
         <name>SciaRegsFile</name>
         <run_address>0x7050</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-75"/>
         </contents>
      </logical_group>
      <logical_group id="lg-44" display="no" color="cyan">
         <name>XIntruptRegsFile</name>
         <run_address>0x7070</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-76"/>
         </contents>
      </logical_group>
      <logical_group id="lg-45" display="no" color="cyan">
         <name>AdcRegsFile</name>
         <run_address>0x7100</run_address>
         <size>0x1e</size>
         <contents>
            <object_component_ref idref="oc-7a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-46" display="no" color="cyan">
         <name>ScibRegsFile</name>
         <run_address>0x7750</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-73"/>
         </contents>
      </logical_group>
      <logical_group id="lg-47" display="no" color="cyan">
         <name>ScicRegsFile</name>
         <run_address>0x7770</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-72"/>
         </contents>
      </logical_group>
      <logical_group id="lg-48" display="no" color="cyan">
         <name>I2caRegsFile</name>
         <run_address>0x7900</run_address>
         <size>0x22</size>
         <contents>
            <object_component_ref idref="oc-84"/>
         </contents>
      </logical_group>
      <logical_group id="lg-49" display="no" color="cyan">
         <name>CsmPwlFile</name>
         <run_address>0x33fff8</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-70"/>
         </contents>
      </logical_group>
      <logical_group id="lg-122" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x205bc</size>
         <contents>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-12e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-124" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x129a</size>
         <contents>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-cc"/>
         </contents>
      </logical_group>
      <logical_group id="lg-126" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xd78</size>
         <contents>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-cd"/>
         </contents>
      </logical_group>
      <logical_group id="lg-128" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb97</size>
         <contents>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-12f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12a" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x498</size>
         <contents>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-5a"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-cf"/>
         </contents>
      </logical_group>
   </logical_group_list>
   <placement_map>
      <memory_area display="no" color="green">
         <name>BEGIN</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x2</length>
         <used_space>0x2</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x2</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>BOOT_RSVD</name>
         <page_id>0x0</page_id>
         <origin>0x2</origin>
         <length>0x4e</length>
         <used_space>0x0</used_space>
         <unused_space>0x4e</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMM0</name>
         <page_id>0x0</page_id>
         <origin>0x50</origin>
         <length>0x3b0</length>
         <used_space>0x0</used_space>
         <unused_space>0x3b0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAML0</name>
         <page_id>0x0</page_id>
         <origin>0x8000</origin>
         <length>0x1000</length>
         <used_space>0x1a</used_space>
         <unused_space>0xfe6</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x8000</start_address>
               <size>0x16</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x8016</start_address>
               <size>0x4</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <available_space>
               <start_address>0x801a</start_address>
               <size>0xfe6</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAML1</name>
         <page_id>0x0</page_id>
         <origin>0x9000</origin>
         <length>0x1000</length>
         <used_space>0x545</used_space>
         <unused_space>0xabb</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x9000</start_address>
               <size>0x545</size>
               <logical_group_ref idref="lg-4"/>
            </allocated_space>
            <available_space>
               <start_address>0x9545</start_address>
               <size>0xabb</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAML2</name>
         <page_id>0x0</page_id>
         <origin>0xa000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAML3</name>
         <page_id>0x0</page_id>
         <origin>0xb000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE6A</name>
         <page_id>0x0</page_id>
         <origin>0x100000</origin>
         <length>0xfc00</length>
         <used_space>0x0</used_space>
         <unused_space>0xfc00</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>CSM_RSVD</name>
         <page_id>0x0</page_id>
         <origin>0x33ff80</origin>
         <length>0x76</length>
         <used_space>0x0</used_space>
         <unused_space>0x76</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CSM_PWL</name>
         <page_id>0x0</page_id>
         <origin>0x33fff8</origin>
         <length>0x8</length>
         <used_space>0x0</used_space>
         <unused_space>0x8</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ADC_CAL</name>
         <page_id>0x0</page_id>
         <origin>0x380080</origin>
         <length>0x9</length>
         <used_space>0x7</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x380080</start_address>
               <size>0x7</size>
               <logical_group_ref idref="lg-18"/>
            </allocated_space>
            <available_space>
               <start_address>0x380087</start_address>
               <size>0x2</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>IQTABLES</name>
         <page_id>0x0</page_id>
         <origin>0x3fe000</origin>
         <length>0xb50</length>
         <used_space>0x0</used_space>
         <unused_space>0xb50</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>IQTABLES2</name>
         <page_id>0x0</page_id>
         <origin>0x3feb50</origin>
         <length>0x8c</length>
         <used_space>0x0</used_space>
         <unused_space>0x8c</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FPUTABLES</name>
         <page_id>0x0</page_id>
         <origin>0x3febdc</origin>
         <length>0x6a0</length>
         <used_space>0x0</used_space>
         <unused_space>0x6a0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BOOTROM</name>
         <page_id>0x0</page_id>
         <origin>0x3ff27c</origin>
         <length>0xd44</length>
         <used_space>0x0</used_space>
         <unused_space>0xd44</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>RESET</name>
         <page_id>0x0</page_id>
         <origin>0x3fffc0</origin>
         <length>0x2</length>
         <used_space>0x0</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMM1</name>
         <page_id>0x1</page_id>
         <origin>0x400</origin>
         <length>0x400</length>
         <used_space>0x300</used_space>
         <unused_space>0x100</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x400</start_address>
               <size>0x300</size>
               <logical_group_ref idref="lg-8"/>
            </allocated_space>
            <available_space>
               <start_address>0x700</start_address>
               <size>0x100</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>DEV_EMU</name>
         <page_id>0x1</page_id>
         <origin>0x880</origin>
         <length>0x180</length>
         <used_space>0xd0</used_space>
         <unused_space>0xb0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x880</start_address>
               <size>0xd0</size>
               <logical_group_ref idref="lg-1a"/>
            </allocated_space>
            <available_space>
               <start_address>0x950</start_address>
               <size>0xb0</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>FLASH_REGS</name>
         <page_id>0x1</page_id>
         <origin>0xa80</origin>
         <length>0x60</length>
         <used_space>0x8</used_space>
         <unused_space>0x58</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xa80</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-1b"/>
            </allocated_space>
            <available_space>
               <start_address>0xa88</start_address>
               <size>0x58</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CSM</name>
         <page_id>0x1</page_id>
         <origin>0xae0</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xae0</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-1c"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ADC_MIRROR</name>
         <page_id>0x1</page_id>
         <origin>0xb00</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xb00</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-1d"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>XINTF</name>
         <page_id>0x1</page_id>
         <origin>0xb20</origin>
         <length>0x20</length>
         <used_space>0x1e</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xb20</start_address>
               <size>0x1e</size>
               <logical_group_ref idref="lg-1e"/>
            </allocated_space>
            <available_space>
               <start_address>0xb3e</start_address>
               <size>0x2</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CPU_TIMER0</name>
         <page_id>0x1</page_id>
         <origin>0xc00</origin>
         <length>0x8</length>
         <used_space>0x8</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xc00</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-1f"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CPU_TIMER1</name>
         <page_id>0x1</page_id>
         <origin>0xc08</origin>
         <length>0x8</length>
         <used_space>0x8</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xc08</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-20"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CPU_TIMER2</name>
         <page_id>0x1</page_id>
         <origin>0xc10</origin>
         <length>0x8</length>
         <used_space>0x8</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xc10</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-21"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>PIE_CTRL</name>
         <page_id>0x1</page_id>
         <origin>0xce0</origin>
         <length>0x20</length>
         <used_space>0x1a</used_space>
         <unused_space>0x6</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xce0</start_address>
               <size>0x1a</size>
               <logical_group_ref idref="lg-22"/>
            </allocated_space>
            <available_space>
               <start_address>0xcfa</start_address>
               <size>0x6</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>PIE_VECT</name>
         <page_id>0x1</page_id>
         <origin>0xd00</origin>
         <length>0x100</length>
         <used_space>0x100</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xd00</start_address>
               <size>0x100</size>
               <logical_group_ref idref="lg-19"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>DMA</name>
         <page_id>0x1</page_id>
         <origin>0x1000</origin>
         <length>0x200</length>
         <used_space>0xe0</used_space>
         <unused_space>0x120</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x1000</start_address>
               <size>0xe0</size>
               <logical_group_ref idref="lg-23"/>
            </allocated_space>
            <available_space>
               <start_address>0x10e0</start_address>
               <size>0x120</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>MCBSPA</name>
         <page_id>0x1</page_id>
         <origin>0x5000</origin>
         <length>0x40</length>
         <used_space>0x25</used_space>
         <unused_space>0x1b</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5000</start_address>
               <size>0x25</size>
               <logical_group_ref idref="lg-24"/>
            </allocated_space>
            <available_space>
               <start_address>0x5025</start_address>
               <size>0x1b</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>MCBSPB</name>
         <page_id>0x1</page_id>
         <origin>0x5040</origin>
         <length>0x40</length>
         <used_space>0x25</used_space>
         <unused_space>0x1b</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5040</start_address>
               <size>0x25</size>
               <logical_group_ref idref="lg-25"/>
            </allocated_space>
            <available_space>
               <start_address>0x5065</start_address>
               <size>0x1b</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECANA</name>
         <page_id>0x1</page_id>
         <origin>0x6000</origin>
         <length>0x40</length>
         <used_space>0x34</used_space>
         <unused_space>0xc</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6000</start_address>
               <size>0x34</size>
               <logical_group_ref idref="lg-26"/>
            </allocated_space>
            <available_space>
               <start_address>0x6034</start_address>
               <size>0xc</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECANA_LAM</name>
         <page_id>0x1</page_id>
         <origin>0x6040</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6040</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-27"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECANA_MOTS</name>
         <page_id>0x1</page_id>
         <origin>0x6080</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6080</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-29"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECANA_MOTO</name>
         <page_id>0x1</page_id>
         <origin>0x60c0</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x60c0</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-2a"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ECANA_MBOX</name>
         <page_id>0x1</page_id>
         <origin>0x6100</origin>
         <length>0x100</length>
         <used_space>0x100</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6100</start_address>
               <size>0x100</size>
               <logical_group_ref idref="lg-28"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECANB</name>
         <page_id>0x1</page_id>
         <origin>0x6200</origin>
         <length>0x40</length>
         <used_space>0x34</used_space>
         <unused_space>0xc</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6200</start_address>
               <size>0x34</size>
               <logical_group_ref idref="lg-2b"/>
            </allocated_space>
            <available_space>
               <start_address>0x6234</start_address>
               <size>0xc</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECANB_LAM</name>
         <page_id>0x1</page_id>
         <origin>0x6240</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6240</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-2c"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECANB_MOTS</name>
         <page_id>0x1</page_id>
         <origin>0x6280</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6280</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-2e"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECANB_MOTO</name>
         <page_id>0x1</page_id>
         <origin>0x62c0</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x62c0</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-2f"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ECANB_MBOX</name>
         <page_id>0x1</page_id>
         <origin>0x6300</origin>
         <length>0x100</length>
         <used_space>0x100</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6300</start_address>
               <size>0x100</size>
               <logical_group_ref idref="lg-2d"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>EPWM1</name>
         <page_id>0x1</page_id>
         <origin>0x6800</origin>
         <length>0x22</length>
         <used_space>0x22</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6800</start_address>
               <size>0x22</size>
               <logical_group_ref idref="lg-30"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>EPWM2</name>
         <page_id>0x1</page_id>
         <origin>0x6840</origin>
         <length>0x22</length>
         <used_space>0x22</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6840</start_address>
               <size>0x22</size>
               <logical_group_ref idref="lg-31"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>EPWM3</name>
         <page_id>0x1</page_id>
         <origin>0x6880</origin>
         <length>0x22</length>
         <used_space>0x22</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6880</start_address>
               <size>0x22</size>
               <logical_group_ref idref="lg-32"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>EPWM4</name>
         <page_id>0x1</page_id>
         <origin>0x68c0</origin>
         <length>0x22</length>
         <used_space>0x22</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x68c0</start_address>
               <size>0x22</size>
               <logical_group_ref idref="lg-33"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>EPWM5</name>
         <page_id>0x1</page_id>
         <origin>0x6900</origin>
         <length>0x22</length>
         <used_space>0x22</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6900</start_address>
               <size>0x22</size>
               <logical_group_ref idref="lg-34"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>EPWM6</name>
         <page_id>0x1</page_id>
         <origin>0x6940</origin>
         <length>0x22</length>
         <used_space>0x22</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6940</start_address>
               <size>0x22</size>
               <logical_group_ref idref="lg-35"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECAP1</name>
         <page_id>0x1</page_id>
         <origin>0x6a00</origin>
         <length>0x20</length>
         <used_space>0x20</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6a00</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-36"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECAP2</name>
         <page_id>0x1</page_id>
         <origin>0x6a20</origin>
         <length>0x20</length>
         <used_space>0x20</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6a20</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-37"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECAP3</name>
         <page_id>0x1</page_id>
         <origin>0x6a40</origin>
         <length>0x20</length>
         <used_space>0x20</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6a40</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-38"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECAP4</name>
         <page_id>0x1</page_id>
         <origin>0x6a60</origin>
         <length>0x20</length>
         <used_space>0x20</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6a60</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-39"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECAP5</name>
         <page_id>0x1</page_id>
         <origin>0x6a80</origin>
         <length>0x20</length>
         <used_space>0x20</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6a80</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-3a"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECAP6</name>
         <page_id>0x1</page_id>
         <origin>0x6aa0</origin>
         <length>0x20</length>
         <used_space>0x20</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6aa0</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-3b"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>EQEP1</name>
         <page_id>0x1</page_id>
         <origin>0x6b00</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6b00</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-3c"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>EQEP2</name>
         <page_id>0x1</page_id>
         <origin>0x6b40</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6b40</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-3d"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>GPIOCTRL</name>
         <page_id>0x1</page_id>
         <origin>0x6f80</origin>
         <length>0x40</length>
         <used_space>0x2e</used_space>
         <unused_space>0x12</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6f80</start_address>
               <size>0x2e</size>
               <logical_group_ref idref="lg-3e"/>
            </allocated_space>
            <available_space>
               <start_address>0x6fae</start_address>
               <size>0x12</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>GPIODAT</name>
         <page_id>0x1</page_id>
         <origin>0x6fc0</origin>
         <length>0x20</length>
         <used_space>0x20</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6fc0</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-3f"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>GPIOINT</name>
         <page_id>0x1</page_id>
         <origin>0x6fe0</origin>
         <length>0x20</length>
         <used_space>0xa</used_space>
         <unused_space>0x16</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6fe0</start_address>
               <size>0xa</size>
               <logical_group_ref idref="lg-40"/>
            </allocated_space>
            <available_space>
               <start_address>0x6fea</start_address>
               <size>0x16</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>SYSTEM</name>
         <page_id>0x1</page_id>
         <origin>0x7010</origin>
         <length>0x20</length>
         <used_space>0x20</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7010</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-41"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>SPIA</name>
         <page_id>0x1</page_id>
         <origin>0x7040</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7040</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-42"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>SCIA</name>
         <page_id>0x1</page_id>
         <origin>0x7050</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7050</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-43"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>XINTRUPT</name>
         <page_id>0x1</page_id>
         <origin>0x7070</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7070</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-44"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ADC</name>
         <page_id>0x1</page_id>
         <origin>0x7100</origin>
         <length>0x20</length>
         <used_space>0x1e</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7100</start_address>
               <size>0x1e</size>
               <logical_group_ref idref="lg-45"/>
            </allocated_space>
            <available_space>
               <start_address>0x711e</start_address>
               <size>0x2</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>SCIB</name>
         <page_id>0x1</page_id>
         <origin>0x7750</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7750</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-46"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>SCIC</name>
         <page_id>0x1</page_id>
         <origin>0x7770</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7770</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-47"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>I2CA</name>
         <page_id>0x1</page_id>
         <origin>0x7900</origin>
         <length>0x40</length>
         <used_space>0x22</used_space>
         <unused_space>0x1e</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7900</start_address>
               <size>0x22</size>
               <logical_group_ref idref="lg-48"/>
            </allocated_space>
            <available_space>
               <start_address>0x7922</start_address>
               <size>0x1e</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAML4</name>
         <page_id>0x1</page_id>
         <origin>0xc000</origin>
         <length>0x1000</length>
         <used_space>0x8</used_space>
         <unused_space>0xff8</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xc000</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-9"/>
            </allocated_space>
            <available_space>
               <start_address>0xc008</start_address>
               <size>0xff8</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAML5</name>
         <page_id>0x1</page_id>
         <origin>0xd000</origin>
         <length>0x1000</length>
         <used_space>0x100</used_space>
         <unused_space>0xf00</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xd000</start_address>
               <size>0x100</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <available_space>
               <start_address>0xd100</start_address>
               <size>0xf00</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAML6</name>
         <page_id>0x1</page_id>
         <origin>0xe000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAML7</name>
         <page_id>0x1</page_id>
         <origin>0xf000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE7B</name>
         <page_id>0x1</page_id>
         <origin>0x20fc00</origin>
         <length>0x400</length>
         <used_space>0x0</used_space>
         <unused_space>0x400</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CSM_PWL</name>
         <page_id>0x1</page_id>
         <origin>0x33fff8</origin>
         <length>0x8</length>
         <used_space>0x8</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x33fff8</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-49"/>
            </allocated_space>
         </usage_details>
      </memory_area>
   </placement_map>
   <symbol_table>
      <symbol id="sm-0">
         <name>cinit</name>
         <value>0x8000</value>
      </symbol>
      <symbol id="sm-1">
         <name>___cinit__</name>
         <value>0x8000</value>
      </symbol>
      <symbol id="sm-2">
         <name>pinit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3">
         <name>___pinit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-4">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-5">
         <name>___binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-6">
         <name>__STACK_SIZE</name>
         <value>0x300</value>
      </symbol>
      <symbol id="sm-7">
         <name>__STACK_END</name>
         <value>0x700</value>
      </symbol>
      <symbol id="sm-8">
         <name>___c_args__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-9">
         <name>.text</name>
         <value>0x9000</value>
      </symbol>
      <symbol id="sm-a">
         <name>___text__</name>
         <value>0x9000</value>
      </symbol>
      <symbol id="sm-b">
         <name>etext</name>
         <value>0x9545</value>
      </symbol>
      <symbol id="sm-c">
         <name>___etext__</name>
         <value>0x9545</value>
      </symbol>
      <symbol id="sm-b8">
         <name>_main</name>
         <value>0x94dd</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-b9">
         <name>_LED_Init</name>
         <value>0x94c9</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-ba">
         <name>_delay</name>
         <value>0x94b1</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-c6">
         <name>_DSP28x_usDelay</name>
         <value>0x8016</value>
         <object_component_ref idref="oc-51"/>
      </symbol>
      <symbol id="sm-dc">
         <name>_InitPll</name>
         <value>0x933e</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-dd">
         <name>_DisableDog</name>
         <value>0x9336</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-de">
         <name>_InitPeripheralClocks</name>
         <value>0x938f</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-df">
         <name>_CsmUnlock</name>
         <value>0x93ea</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-e0">
         <name>_ServiceDog</name>
         <value>0x932c</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-e1">
         <name>_InitSysCtrl</name>
         <value>0x9323</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-ee">
         <name>_PieVectTableInit</name>
         <value>0xd000</value>
         <object_component_ref idref="oc-5c"/>
      </symbol>
      <symbol id="sm-16b">
         <name>_GpioCtrlRegs</name>
         <value>0x6f80</value>
         <object_component_ref idref="oc-8d"/>
      </symbol>
      <symbol id="sm-16c">
         <name>_PieVectTable</name>
         <value>0xd00</value>
         <object_component_ref idref="oc-9c"/>
      </symbol>
      <symbol id="sm-16d">
         <name>_ECap4Regs</name>
         <value>0x6a60</value>
         <object_component_ref idref="oc-7c"/>
      </symbol>
      <symbol id="sm-16e">
         <name>_CsmRegs</name>
         <value>0xae0</value>
         <object_component_ref idref="oc-78"/>
      </symbol>
      <symbol id="sm-16f">
         <name>_ECanaLAMRegs</name>
         <value>0x6040</value>
         <object_component_ref idref="oc-92"/>
      </symbol>
      <symbol id="sm-170">
         <name>_ECanbMOTORegs</name>
         <value>0x62c0</value>
         <object_component_ref idref="oc-94"/>
      </symbol>
      <symbol id="sm-171">
         <name>_EPwm4Regs</name>
         <value>0x68c0</value>
         <object_component_ref idref="oc-89"/>
      </symbol>
      <symbol id="sm-172">
         <name>_ECap5Regs</name>
         <value>0x6a80</value>
         <object_component_ref idref="oc-7d"/>
      </symbol>
      <symbol id="sm-173">
         <name>_CpuTimer1Regs</name>
         <value>0xc08</value>
         <object_component_ref idref="oc-6d"/>
      </symbol>
      <symbol id="sm-174">
         <name>_SysCtrlRegs</name>
         <value>0x7010</value>
         <object_component_ref idref="oc-81"/>
      </symbol>
      <symbol id="sm-175">
         <name>_EPwm5Regs</name>
         <value>0x6900</value>
         <object_component_ref idref="oc-88"/>
      </symbol>
      <symbol id="sm-176">
         <name>_SpiaRegs</name>
         <value>0x7040</value>
         <object_component_ref idref="oc-74"/>
      </symbol>
      <symbol id="sm-177">
         <name>_ECanaMOTSRegs</name>
         <value>0x6080</value>
         <object_component_ref idref="oc-93"/>
      </symbol>
      <symbol id="sm-178">
         <name>_ECap6Regs</name>
         <value>0x6aa0</value>
         <object_component_ref idref="oc-83"/>
      </symbol>
      <symbol id="sm-179">
         <name>_DmaRegs</name>
         <value>0x1000</value>
         <object_component_ref idref="oc-99"/>
      </symbol>
      <symbol id="sm-17a">
         <name>_FlashRegs</name>
         <value>0xa80</value>
         <object_component_ref idref="oc-6e"/>
      </symbol>
      <symbol id="sm-17b">
         <name>_CpuTimer0Regs</name>
         <value>0xc00</value>
         <object_component_ref idref="oc-6f"/>
      </symbol>
      <symbol id="sm-17c">
         <name>_DevEmuRegs</name>
         <value>0x880</value>
         <object_component_ref idref="oc-98"/>
      </symbol>
      <symbol id="sm-17d">
         <name>_McbspbRegs</name>
         <value>0x5040</value>
         <object_component_ref idref="oc-8c"/>
      </symbol>
      <symbol id="sm-17e">
         <name>_EPwm6Regs</name>
         <value>0x6940</value>
         <object_component_ref idref="oc-8a"/>
      </symbol>
      <symbol id="sm-17f">
         <name>_SciaRegs</name>
         <value>0x7050</value>
         <object_component_ref idref="oc-75"/>
      </symbol>
      <symbol id="sm-180">
         <name>_GpioDataRegs</name>
         <value>0x6fc0</value>
         <object_component_ref idref="oc-80"/>
      </symbol>
      <symbol id="sm-181">
         <name>_CsmPwl</name>
         <value>0x33fff8</value>
         <object_component_ref idref="oc-70"/>
      </symbol>
      <symbol id="sm-182">
         <name>_AdcRegs</name>
         <value>0x7100</value>
         <object_component_ref idref="oc-7a"/>
      </symbol>
      <symbol id="sm-183">
         <name>_XIntruptRegs</name>
         <value>0x7070</value>
         <object_component_ref idref="oc-76"/>
      </symbol>
      <symbol id="sm-184">
         <name>_CpuTimer2Regs</name>
         <value>0xc10</value>
         <object_component_ref idref="oc-6c"/>
      </symbol>
      <symbol id="sm-185">
         <name>_PieCtrlRegs</name>
         <value>0xce0</value>
         <object_component_ref idref="oc-79"/>
      </symbol>
      <symbol id="sm-186">
         <name>_ECanbMOTSRegs</name>
         <value>0x6280</value>
         <object_component_ref idref="oc-91"/>
      </symbol>
      <symbol id="sm-187">
         <name>_ECanaRegs</name>
         <value>0x6000</value>
         <object_component_ref idref="oc-8f"/>
      </symbol>
      <symbol id="sm-188">
         <name>_AdcMirror</name>
         <value>0xb00</value>
         <object_component_ref idref="oc-77"/>
      </symbol>
      <symbol id="sm-189">
         <name>_ECanbMboxes</name>
         <value>0x6300</value>
         <object_component_ref idref="oc-9a"/>
      </symbol>
      <symbol id="sm-18a">
         <name>_XintfRegs</name>
         <value>0xb20</value>
         <object_component_ref idref="oc-7b"/>
      </symbol>
      <symbol id="sm-18b">
         <name>_ScicRegs</name>
         <value>0x7770</value>
         <object_component_ref idref="oc-72"/>
      </symbol>
      <symbol id="sm-18c">
         <name>_ECap1Regs</name>
         <value>0x6a00</value>
         <object_component_ref idref="oc-82"/>
      </symbol>
      <symbol id="sm-18d">
         <name>_EQep1Regs</name>
         <value>0x6b00</value>
         <object_component_ref idref="oc-97"/>
      </symbol>
      <symbol id="sm-18e">
         <name>_McbspaRegs</name>
         <value>0x5000</value>
         <object_component_ref idref="oc-8b"/>
      </symbol>
      <symbol id="sm-18f">
         <name>_EPwm1Regs</name>
         <value>0x6800</value>
         <object_component_ref idref="oc-86"/>
      </symbol>
      <symbol id="sm-190">
         <name>_ECanbRegs</name>
         <value>0x6200</value>
         <object_component_ref idref="oc-8e"/>
      </symbol>
      <symbol id="sm-191">
         <name>_ScibRegs</name>
         <value>0x7750</value>
         <object_component_ref idref="oc-73"/>
      </symbol>
      <symbol id="sm-192">
         <name>_ECap2Regs</name>
         <value>0x6a20</value>
         <object_component_ref idref="oc-7e"/>
      </symbol>
      <symbol id="sm-193">
         <name>_GpioIntRegs</name>
         <value>0x6fe0</value>
         <object_component_ref idref="oc-71"/>
      </symbol>
      <symbol id="sm-194">
         <name>_EQep2Regs</name>
         <value>0x6b40</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-195">
         <name>_EPwm2Regs</name>
         <value>0x6840</value>
         <object_component_ref idref="oc-85"/>
      </symbol>
      <symbol id="sm-196">
         <name>_ECanaMboxes</name>
         <value>0x6100</value>
         <object_component_ref idref="oc-9b"/>
      </symbol>
      <symbol id="sm-197">
         <name>_ECap3Regs</name>
         <value>0x6a40</value>
         <object_component_ref idref="oc-7f"/>
      </symbol>
      <symbol id="sm-198">
         <name>_ECanaMOTORegs</name>
         <value>0x60c0</value>
         <object_component_ref idref="oc-90"/>
      </symbol>
      <symbol id="sm-199">
         <name>_EPwm3Regs</name>
         <value>0x6880</value>
         <object_component_ref idref="oc-87"/>
      </symbol>
      <symbol id="sm-19a">
         <name>_ECanbLAMRegs</name>
         <value>0x6240</value>
         <object_component_ref idref="oc-96"/>
      </symbol>
      <symbol id="sm-19b">
         <name>_I2caRegs</name>
         <value>0x7900</value>
         <object_component_ref idref="oc-84"/>
      </symbol>
      <symbol id="sm-1f7">
         <name>_ILLEGAL_ISR</name>
         <value>0x903c</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-1f8">
         <name>_EPWM6_INT_ISR</name>
         <value>0x9172</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-1f9">
         <name>_DATALOG_ISR</name>
         <value>0x9014</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-1fa">
         <name>_SPITXINTA_ISR</name>
         <value>0x91d6</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-1fb">
         <name>_SPIRXINTA_ISR</name>
         <value>0x91cc</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-1fc">
         <name>_DINTCH3_ISR</name>
         <value>0x921c</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-1fd">
         <name>_XINT4_ISR</name>
         <value>0x92c6</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-1fe">
         <name>_SEQ1INT_ISR</name>
         <value>0x90be</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-1ff">
         <name>_ECAP3_INT_ISR</name>
         <value>0x9190</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-200">
         <name>_INT13_ISR</name>
         <value>0x9000</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-201">
         <name>_MXINTA_ISR</name>
         <value>0x91fe</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-202">
         <name>_EPWM4_INT_ISR</name>
         <value>0x915e</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-203">
         <name>_USER5_ISR</name>
         <value>0x906e</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-204">
         <name>_XINT7_ISR</name>
         <value>0x92e4</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-205">
         <name>_EMPTY_ISR</name>
         <value>0x9302</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-206">
         <name>_EPWM5_TZINT_ISR</name>
         <value>0x912c</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-207">
         <name>_EPWM4_TZINT_ISR</name>
         <value>0x9122</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-208">
         <name>_ECAN0INTA_ISR</name>
         <value>0x9294</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-209">
         <name>_EPWM6_TZINT_ISR</name>
         <value>0x9136</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-20a">
         <name>_EMUINT_ISR</name>
         <value>0x9028</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-20b">
         <name>_ECAP1_INT_ISR</name>
         <value>0x917c</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-20c">
         <name>_EPWM1_TZINT_ISR</name>
         <value>0x9104</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-20d">
         <name>_EQEP2_INT_ISR</name>
         <value>0x91c2</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-20e">
         <name>_USER11_ISR</name>
         <value>0x90aa</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-20f">
         <name>_EPWM3_TZINT_ISR</name>
         <value>0x9118</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-210">
         <name>_USER4_ISR</name>
         <value>0x9064</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-211">
         <name>_EPWM2_TZINT_ISR</name>
         <value>0x910e</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-212">
         <name>_XINT6_ISR</name>
         <value>0x92da</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-213">
         <name>_EPWM2_INT_ISR</name>
         <value>0x914a</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-214">
         <name>_ECAN0INTB_ISR</name>
         <value>0x92a8</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-215">
         <name>_TINT0_ISR</name>
         <value>0x90f0</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-216">
         <name>_WAKEINT_ISR</name>
         <value>0x90fa</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-217">
         <name>_DINTCH4_ISR</name>
         <value>0x9226</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-218">
         <name>_USER10_ISR</name>
         <value>0x90a0</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-219">
         <name>_USER7_ISR</name>
         <value>0x9082</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-21a">
         <name>_XINT1_ISR</name>
         <value>0x90d2</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-21b">
         <name>_ECAP6_INT_ISR</name>
         <value>0x91ae</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-21c">
         <name>_INT14_ISR</name>
         <value>0x900a</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-21d">
         <name>_MXINTB_ISR</name>
         <value>0x91ea</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-21e">
         <name>_DINTCH5_ISR</name>
         <value>0x9230</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-21f">
         <name>_USER6_ISR</name>
         <value>0x9078</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-220">
         <name>_ECAP4_INT_ISR</name>
         <value>0x919a</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-221">
         <name>_MRINTA_ISR</name>
         <value>0x91f4</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-222">
         <name>_DINTCH6_ISR</name>
         <value>0x923a</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-223">
         <name>_USER12_ISR</name>
         <value>0x90b4</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-224">
         <name>_ADCINT_ISR</name>
         <value>0x90e6</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-225">
         <name>_USER1_ISR</name>
         <value>0x9046</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-226">
         <name>_XINT3_ISR</name>
         <value>0x92bc</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-227">
         <name>_EPWM5_INT_ISR</name>
         <value>0x9168</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-228">
         <name>_NMI_ISR</name>
         <value>0x9032</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-229">
         <name>_SCITXINTB_ISR</name>
         <value>0x928a</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-22a">
         <name>_SCIRXINTB_ISR</name>
         <value>0x9280</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-22b">
         <name>_ECAN1INTA_ISR</name>
         <value>0x929e</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-22c">
         <name>_ECAP2_INT_ISR</name>
         <value>0x9186</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-22d">
         <name>_PIE_RESERVED</name>
         <value>0x930f</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-22e">
         <name>_I2CINT1A_ISR</name>
         <value>0x9244</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-22f">
         <name>_XINT2_ISR</name>
         <value>0x90dc</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-230">
         <name>_I2CINT2A_ISR</name>
         <value>0x924e</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-231">
         <name>_SCITXINTC_ISR</name>
         <value>0x9262</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-232">
         <name>_SCIRXINTC_ISR</name>
         <value>0x9258</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-233">
         <name>_RTOSINT_ISR</name>
         <value>0x901e</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-234">
         <name>_EPWM3_INT_ISR</name>
         <value>0x9154</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-235">
         <name>_ECAN1INTB_ISR</name>
         <value>0x92b2</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-236">
         <name>_USER9_ISR</name>
         <value>0x9096</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-237">
         <name>_USER3_ISR</name>
         <value>0x905a</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-238">
         <name>_EQEP1_INT_ISR</name>
         <value>0x91b8</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-239">
         <name>_MRINTB_ISR</name>
         <value>0x91e0</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-23a">
         <name>_DINTCH1_ISR</name>
         <value>0x9208</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-23b">
         <name>_USER8_ISR</name>
         <value>0x908c</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-23c">
         <name>_EPWM1_INT_ISR</name>
         <value>0x9140</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-23d">
         <name>_SEQ2INT_ISR</name>
         <value>0x90c8</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-23e">
         <name>_USER2_ISR</name>
         <value>0x9050</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-23f">
         <name>_LUF_ISR</name>
         <value>0x92f8</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-240">
         <name>_LVF_ISR</name>
         <value>0x92ee</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-241">
         <name>_SCITXINTA_ISR</name>
         <value>0x9276</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-242">
         <name>_SCIRXINTA_ISR</name>
         <value>0x926c</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-243">
         <name>_rsvd_ISR</name>
         <value>0x9319</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-244">
         <name>_ECAP5_INT_ISR</name>
         <value>0x91a4</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-245">
         <name>_DINTCH2_ISR</name>
         <value>0x9212</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-246">
         <name>_XINT5_ISR</name>
         <value>0x92d0</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-256">
         <name>code_start</name>
         <value>0x0</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-262">
         <name>_ADC_cal</name>
         <value>0x380080</value>
         <object_component_ref idref="oc-aa"/>
      </symbol>
      <symbol id="sm-27e">
         <name>_c_int00</name>
         <value>0x941b</value>
         <object_component_ref idref="oc-4b"/>
      </symbol>
      <symbol id="sm-27f">
         <name>__stack</name>
         <value>0x400</value>
         <object_component_ref idref="oc-af"/>
      </symbol>
      <symbol id="sm-295">
         <name>_copy_in</name>
         <value>0x946b</value>
         <object_component_ref idref="oc-d0"/>
      </symbol>
      <symbol id="sm-2a4">
         <name>_ppcopy</name>
         <value>0x94e8</value>
         <object_component_ref idref="oc-ba"/>
      </symbol>
      <symbol id="sm-2a5">
         <name>_dpcopy</name>
         <value>0x94e8</value>
         <object_component_ref idref="oc-ba"/>
      </symbol>
      <symbol id="sm-2a6">
         <name>_pdcopy</name>
         <value>0x94e8</value>
         <object_component_ref idref="oc-ba"/>
      </symbol>
      <symbol id="sm-2a7">
         <name>_ddcopy</name>
         <value>0x94e8</value>
         <object_component_ref idref="oc-ba"/>
      </symbol>
      <symbol id="sm-2bb">
         <name>C$$EXIT</name>
         <value>0x951b</value>
         <object_component_ref idref="oc-d2"/>
      </symbol>
      <symbol id="sm-2bc">
         <name>_exit</name>
         <value>0x951d</value>
         <object_component_ref idref="oc-d2"/>
      </symbol>
      <symbol id="sm-2bd">
         <name>___TI_cleanup_ptr</name>
         <value>0xc000</value>
         <object_component_ref idref="oc-c1"/>
      </symbol>
      <symbol id="sm-2be">
         <name>_abort</name>
         <value>0x951b</value>
         <object_component_ref idref="oc-d2"/>
      </symbol>
      <symbol id="sm-2bf">
         <name>___TI_dtors_ptr</name>
         <value>0xc002</value>
         <object_component_ref idref="oc-c1"/>
      </symbol>
      <symbol id="sm-2d4">
         <name>__unlock</name>
         <value>0xc006</value>
         <object_component_ref idref="oc-d9"/>
      </symbol>
      <symbol id="sm-2d5">
         <name>__lock</name>
         <value>0xc004</value>
         <object_component_ref idref="oc-d4"/>
      </symbol>
      <symbol id="sm-2d6">
         <name>__register_lock</name>
         <value>0x9538</value>
         <object_component_ref idref="oc-d7"/>
      </symbol>
      <symbol id="sm-2d7">
         <name>__nop</name>
         <value>0x953c</value>
         <object_component_ref idref="oc-d7"/>
      </symbol>
      <symbol id="sm-2d8">
         <name>__register_unlock</name>
         <value>0x9534</value>
         <object_component_ref idref="oc-d7"/>
      </symbol>
      <symbol id="sm-2e8">
         <name>__args_main</name>
         <value>0x9502</value>
         <object_component_ref idref="oc-d1"/>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
