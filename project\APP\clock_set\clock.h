/*
 * clock.h
 *
 *  Created on: 2025-7-2
 *      Author: 17165
 */

#ifndef CLOCK_H_
#define CLOCK_H_

#include "DSP2833x_Device.h"     // DSP2833x Headerfile Include File
#include "DSP2833x_Examples.h"   // DSP2833x Examples Include File

//void intToStr(int num, char* str);
char *num_to_str(int num, char *str);
void str_add1(char *a,char *b,char *c,char *outcome);
void str_add2(char *a,char *b,char *c,char *outcome);

#endif /* CLOCK_H_ */
